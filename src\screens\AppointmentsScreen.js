import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, FlatList, SafeAreaView, Alert, Dimensions } from 'react-native';
import { COLORS } from '../theme';

const { width } = Dimensions.get('window');
import { supabase } from '../utils/supabase';
import { useFocusEffect } from '@react-navigation/native';

const FILTERS = [
  { label: 'Pending', value: 'pending' },
  { label: 'Completed', value: 'completed' },
];

const AppointmentsScreen = ({ route, navigation }) => {
  const { doctor } = route.params || {};
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState('pending');

  const fetchTodayPatients = async (statusFilter = filter) => {
    setLoading(true);
    try {
      const today = new Date();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, '0');
      const dd = String(today.getDate()).padStart(2, '0');
      const todayStr = `${yyyy}-${mm}-${dd}`;
      const doctorIdInt = parseInt(doctor.doctor_id, 10);
      console.log('Fetching for doctor_id:', doctorIdInt, 'date:', todayStr, 'status:', statusFilter);
      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('doctor_id', doctorIdInt)
        .eq('date', todayStr)
        .eq('status', statusFilter);
      if (error) throw error;
      console.log('Appointments fetched:', data);
      setAppointments(data || []);
    } catch (e) {
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchPendingAppointments = async () => {
    setLoading(true);
    try {
      const doctorIdInt = parseInt(doctor.doctor_id, 10);
      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('doctor_id', doctorIdInt)
        .eq('status', 'pending');
      if (error) throw error;
      // Sort: today's date first, then older
      const today = new Date();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, '0');
      const dd = String(today.getDate()).padStart(2, '0');
      const todayStr = `${yyyy}-${mm}-${dd}`;
      const sorted = [...(data || [])].sort((a, b) => {
        if (a.date === todayStr && b.date !== todayStr) return -1;
        if (a.date !== todayStr && b.date === todayStr) return 1;
        return b.date.localeCompare(a.date) || b.time.localeCompare(a.time);
      });
      setAppointments(sorted);
    } catch (e) {
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (filter === 'pending') fetchPendingAppointments();
      else fetchTodayPatients('completed');
    }, [doctor, filter])
  );

  const updateStatus = async (appointment_id, status) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('appointments')
        .update({ status })
        .eq('appointment_id', appointment_id);
      if (error) throw error;
      // Refetch after update
      fetchTodayPatients();
    } catch (e) {}
    setLoading(false);
  };

  const deleteAppointment = async (appointment_id) => {
    if (!appointment_id) return;
    if (!window.confirm) {
      // React Native Alert
      Alert.alert('Delete Appointment', 'Are you sure you want to delete this appointment?', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: async () => {
          setLoading(true);
          await supabase.from('appointments').delete().eq('appointment_id', appointment_id);
          setLoading(false);
          navigation.navigate('Dashboard', { doctor });
        }}
      ]);
    } else {
      // Web fallback
      if (window.confirm('Are you sure you want to delete this appointment?')) {
        setLoading(true);
        await supabase.from('appointments').delete().eq('appointment_id', appointment_id);
        setLoading(false);
        navigation.navigate('Dashboard', { doctor });
      }
    }
  };

  const renderAppointment = ({ item }) => {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    const todayStr = `${yyyy}-${mm}-${dd}`;
    const isToday = item.date === todayStr;
    return (
      <View style={styles.appointmentCard}>
        <Text style={styles.patientName}>Patient: {item.patient_name}</Text>
        <Text style={styles.info}>Time: {item.time}</Text>
        <Text style={styles.info}>Date: {item.date}</Text>
        <View style={styles.actionContainer}>
          <View style={styles.statusContainer}>
            {filter === 'pending' && isToday ? (
              <View style={styles.actionButtonsContainer}>
                <TouchableOpacity 
                  style={[styles.actionButton, styles.completeButton]}
                  onPress={() => updateStatus(item.appointment_id, 'completed')}
                >
                  <Text style={styles.actionButtonText}>✓ Complete</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.actionButton, styles.cancelButton]}
                  onPress={() => updateStatus(item.appointment_id, 'pending')}
                >
                  <Text style={styles.actionButtonText}>✗ Cancel</Text>
                </TouchableOpacity>
              </View>
            ) : filter === 'pending' && !isToday ? (
              <View style={[styles.statusLabel, styles.statusLabelOldPending]}>
                <Text style={styles.statusTextOldPending}>Old Pending</Text>
              </View>
            ) : (
              <View style={[
                styles.statusLabel, 
                item.status === 'completed' ? styles.statusLabelCompleted : styles.statusLabelPending
              ]}>
                <Text style={item.status === 'completed' ? styles.statusTextCompleted : styles.statusTextPending}>
                  {item.status === 'completed' ? 'Completed' : 'Pending'}
                </Text>
              </View>
            )}
          </View>
          <TouchableOpacity 
            style={styles.deleteButton}
            onPress={() => deleteAppointment(item.appointment_id)}
          >
            <Text style={styles.deleteButtonText}>🗑</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Today's Appointments</Text>
        <View style={{ width: 40 }} />
      </View>
      <View style={styles.filterContainer}>
        {FILTERS.map(f => (
          <TouchableOpacity
            key={f.value}
            style={[styles.filterButton, filter === f.value && styles.activeFilter]}
            onPress={() => setFilter(f.value)}
          >
            <Text style={[styles.filterText, filter === f.value && styles.activeFilterText]}>{f.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
      {loading ? (
        <ActivityIndicator size="large" color="#3498db" style={{ marginTop: 40 }} />
      ) : appointments.length === 0 ? (
        <Text style={styles.emptyText}>No appointments found.</Text>
      ) : (
        <FlatList
          data={appointments}
          keyExtractor={item => item.appointment_id?.toString() || Math.random().toString()}
          renderItem={renderAppointment}
          contentContainerStyle={{ padding: 15 }}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 10,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '800',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  backButton: {
    width: 80,
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 5,
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.2)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeFilter: {
    backgroundColor: COLORS.primaryDark,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  filterText: {
    color: COLORS.text,
    fontWeight: '600',
    fontSize: 14,
  },
  activeFilterText: {
    color: '#fff',
    fontWeight: '700',
  },
  appointmentCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 18,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.2)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  patientName: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 12,
    letterSpacing: 0.2,
    lineHeight: 24,
  },
  info: {
    fontSize: 15,
    color: COLORS.textLight,
    marginBottom: 6,
    lineHeight: 22,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.textLight,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 40,
    paddingHorizontal: 30,
    lineHeight: 24,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 14,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
  },
  statusContainer: {
    flex: 1,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 100,
  },
  completeButton: {
    backgroundColor: 'rgba(72, 187, 120, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(72, 187, 120, 0.3)',
  },
  cancelButton: {
    backgroundColor: 'rgba(245, 101, 101, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(245, 101, 101, 0.3)',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
  },
  statusLabel: {
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderRadius: 14,
    alignSelf: 'flex-start',
    borderWidth: 1,
    minWidth: 100,
    alignItems: 'center',
  },
  statusLabelCompleted: {
    backgroundColor: COLORS.successLight,
    borderColor: 'rgba(72, 187, 120, 0.2)',
  },
  statusTextCompleted: {
    color: COLORS.success,
    fontWeight: '700',
    fontSize: 14,
  },
  statusLabelPending: {
    backgroundColor: 'rgba(245, 101, 101, 0.1)',
    borderColor: 'rgba(245, 101, 101, 0.2)',
  },
  statusTextPending: {
    color: COLORS.error,
    fontWeight: '700',
    fontSize: 14,
  },
  statusLabelOldPending: {
    backgroundColor: 'rgba(245, 101, 101, 0.1)',
    borderColor: 'rgba(245, 101, 101, 0.2)',
  },
  deleteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(245, 101, 101, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(245, 101, 101, 0.2)',
  },
  deleteButtonText: {
    fontSize: 18,
    color: COLORS.error,
  },
  statusTextOldPending: {
    color: 'red',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default AppointmentsScreen; 