import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import React, { useEffect, useState } from 'react';
import { Alert, Image, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const DiagnosisPage4 = ({ formData, onChange, mrno }) => {
  const [formState, setFormState] = useState({
    provisionalDiagnosis: '',
    additionalInfo: '',
    doctorSignature: '',
    doctorName: '',
    doctorDesignation: '',
    doctorKmcNo: '',
    doctorDate: '',
    doctorTime: '',
    consultantSignature: '',
    consultantName: '',
    consultantDesignation: '',
    consultantKmcNo: '',
    consultantDate: '',
    consultantTime: ''
  });

  // Load saved data when component mounts
  useEffect(() => {
    const loadSavedData = async () => {
      try {
        const keys = await AsyncStorage.getAllKeys();
        const savedKeys = keys.filter(key => key.startsWith(`diagnosisPage4Data_${mrno}_`));
        if (savedKeys.length > 0) {
          savedKeys.sort().reverse();
          const mostRecentKey = savedKeys[0];
          const savedData = await AsyncStorage.getItem(mostRecentKey);
          if (savedData) {
            setFormState(JSON.parse(savedData));
          }
        } else if (formData) {
          setFormState(formData);
        }
      } catch (error) {
        console.error('Error loading saved data:', error);
      }
    };
    loadSavedData();
  }, [formData, mrno]);

  // Update parent component whenever form state changes
  useEffect(() => {
    if (onChange) {
      onChange(formState);
    }
  }, [formState]);

  const handleSave = async () => {
    try {
      const timestamp = new Date().toISOString();
      const key = `diagnosisPage4Data_${mrno}_${timestamp}`;
      await AsyncStorage.setItem(key, JSON.stringify(formState));
      Alert.alert('Success', 'Diagnosis Page 4 data saved permanently!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save data');
    }
  };

  const handleSaveForm = async () => {
    try {
      console.log('Starting PDF generation...');
      
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              @page {
                size: A4;
                margin: 0;
              }
              body { 
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #000;
              }
              .page { 
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #000;
                background: white;
                min-height: 800px;
              }
              .header { 
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
              }
              .header-top-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
              }
              .logo {
                width: 350px;
                height: 100px;
                object-fit: contain;
              }
              .header-title {
                font-size: 20px;
                font-weight: bold;
                margin: 0;
              }
              .header-subtitle {
                font-size: 16px;
                margin: 5px 0;
              }
              .section {
                margin-bottom: 20px;
                border: 1px solid #000;
              }
              .section-header {
                background-color: #f0f0f0;
                padding: 8px;
                border-bottom: 1px solid #000;
              }
              .section-title {
                font-size: 16px;
                font-weight: bold;
                margin: 0;
              }
              .input-area {
                padding: 8px;
                min-height: 150px;
                white-space: pre-wrap;
              }
              .signature-section {
                display: flex;
                border: 1px solid #000;
                margin-top: 20px;
              }
              .signature-column {
                flex: 1;
                border-right: 1px solid #000;
                display: flex;
              }
              .signature-column:last-child {
                border-right: none;
              }
              .vertical-text {
                writing-mode: vertical-rl;
                transform: rotate(180deg);
                background-color: #f0f0f0;
                padding: 10px;
                text-align: center;
                font-weight: bold;
                font-size: 16px;
              }
              .signature-fields {
                flex: 1;
                padding: 10px;
              }
              .field-row {
                display: flex;
                align-items: center;
                margin-bottom: 5px;
              }
              .field-label {
                font-weight: bold;
                min-width: 100px;
                margin-right: 10px;
              }
              .field-value {
                border-bottom: 1px solid #000;
                padding: 2px 5px;
                flex: 1;
              }
              .date-time-row {
                display: flex;
                justify-content: space-between;
                margin-top: 5px;
              }
              .date-time-field {
                display: flex;
                align-items: center;
                flex: 1;
              }
              .date-time-field:first-child {
                margin-right: 20px;
              }
            </style>
          </head>
          <body>
            <div class="page">
              <div class="header">
                <div class="header-top-row">
                  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." class="logo" alt="Hospital Logo"/>
                </div>
                <h1 class="header-title">CURA HOSPITALS</h1>
                <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
                <h2 class="header-subtitle">FORM(4)</h2>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">Provisional Diagnosis</h3>
                </div>
                <div class="input-area">
                  ${formState.provisionalDiagnosis || 'N/A'}
                </div>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">Additional Information</h3>
                </div>
                <div class="input-area">
                  ${formState.additionalInfo || 'N/A'}
                </div>
              </div>

              <div class="signature-section">
                <div class="signature-column">
                  <div class="vertical-text">DOCTOR</div>
                  <div class="signature-fields">
                    <div class="field-row">
                      <span class="field-label">Signature:</span>
                      <span class="field-value">${formState.doctorSignature || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                      <span class="field-label">Name:</span>
                      <span class="field-value">${formState.doctorName || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                      <span class="field-label">Designation:</span>
                      <span class="field-value">${formState.doctorDesignation || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                      <span class="field-label">KMC No:</span>
                      <span class="field-value">${formState.doctorKmcNo || 'N/A'}</span>
                    </div>
                    <div class="date-time-row">
                      <div class="date-time-field">
                        <span class="field-label">Date:</span>
                        <span class="field-value">${formState.doctorDate || 'N/A'}</span>
                      </div>
                      <div class="date-time-field">
                        <span class="field-label">Time:</span>
                        <span class="field-value">${formState.doctorTime || 'N/A'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="signature-column">
                  <div class="vertical-text">PRIMARY CONSULTANT</div>
                  <div class="signature-fields">
                    <div class="field-row">
                      <span class="field-label">Signature:</span>
                      <span class="field-value">${formState.consultantSignature || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                      <span class="field-label">Name:</span>
                      <span class="field-value">${formState.consultantName || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                      <span class="field-label">Designation:</span>
                      <span class="field-value">${formState.consultantDesignation || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                      <span class="field-label">KMC No:</span>
                      <span class="field-value">${formState.consultantKmcNo || 'N/A'}</span>
                    </div>
                    <div class="date-time-row">
                      <div class="date-time-field">
                        <span class="field-label">Date:</span>
                        <span class="field-value">${formState.consultantDate || 'N/A'}</span>
                      </div>
                      <div class="date-time-field">
                        <span class="field-label">Time:</span>
                        <span class="field-value">${formState.consultantTime || 'N/A'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </body>
        </html>
      `;

      console.log('HTML content generated, creating PDF...');
      
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        width: 595, // A4 width in points
        height: 842, // A4 height in points
        base64: false
      });

      console.log('PDF generated at:', uri);

      // Pass the PDF data to the parent component
      if (onChange) {
        onChange({
          ...formState,
          pdfUri: uri,
          formType: 'diagnosisPage4'
        });
      }

      Alert.alert(
        'Success', 
        'PDF generated successfully!',
        [
          {
            text: 'View PDF',
            onPress: async () => {
              try {
                console.log('Opening PDF viewer...');
                const isAvailable = await Sharing.isAvailableAsync();
                if (isAvailable) {
                  await Sharing.shareAsync(uri, {
                    mimeType: 'application/pdf',
                    dialogTitle: 'View Diagnosis Form 4',
                    UTI: 'com.adobe.pdf'
                  });
                } else {
                  Alert.alert('Error', 'Sharing is not available on this device');
                }
              } catch (error) {
                console.error('Error opening PDF:', error);
                Alert.alert('Error', 'Failed to open PDF');
              }
            }
          },
          {
            text: 'OK',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert('Error', 'Failed to generate PDF');
    }
  };

  const handleReset = () => {
    // Reset all form fields to empty
    setFormState({
      provisionalDiagnosis: '',
      additionalInfo: '',
      doctorSignature: '',
      doctorName: '',
      doctorDesignation: '',
      doctorKmcNo: '',
      doctorDate: '',
      doctorTime: '',
      consultantSignature: '',
      consultantName: '',
      consultantDesignation: '',
      consultantKmcNo: '',
      consultantDate: '',
      consultantTime: ''
    });
  };

  return (
    <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        {/* Top Row: Logos */}
        <View style={styles.headerTopRow}>
          <Image
            source={require('../assets/Logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* Centered Text */}
        <View style={styles.headerCenteredText}>
          <Text style={styles.headerTitle}>CURA HOSPITALS</Text>
          <Text style={styles.headerSubtitle}>INPATIENT INITIAL ASSESSMENT</Text>
          <Text style={styles.headerSubtitle}>FORM(4)</Text>
        </View>
      </View>

      {/* Provisional Diagnosis Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Provisional Diagnosis</Text>
        </View>
        <TextInput
          style={[styles.input, styles.largeTextArea]}
          multiline
          numberOfLines={10}
            value={formState.provisionalDiagnosis}
            onChangeText={(text) => setFormState(prev => ({ ...prev, provisionalDiagnosis: text }))}
        />
      </View>

      {/* Additional Information Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Additional Information:</Text>
        </View>
        <TextInput
          style={[styles.input, styles.largeTextArea]}
          multiline
          numberOfLines={10}
            value={formState.additionalInfo}
            onChangeText={(text) => setFormState(prev => ({ ...prev, additionalInfo: text }))}
        />
      </View>

      {/* Doctor and Primary Consultant Sections */}
      <View style={styles.signatureSectionContainer}>
        {/* Doctor Section */}
        <View style={styles.signatureColumn}>
          <View style={styles.verticalTextContainer}>
            <Text style={styles.verticalText}>DOCTOR</Text>
          </View>
          <View style={styles.signatureFields}>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>Signature:</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.doctorSignature}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, doctorSignature: text }))}
                />
            </View>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>Name:</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.doctorName}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, doctorName: text }))}
                />
            </View>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>Designation</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.doctorDesignation}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, doctorDesignation: text }))}
                />
            </View>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>KMC No</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.doctorKmcNo}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, doctorKmcNo: text }))}
                />
            </View>
            <View style={styles.fieldRowDateAndTime}>
              <Text style={styles.label}>Date</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.doctorDate}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, doctorDate: text }))}
                />
              <Text style={styles.label}>Time</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.doctorTime}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, doctorTime: text }))}
                />
            </View>
          </View>
        </View>

        {/* Primary Consultant Section */}
        <View style={styles.signatureColumn}>
          <View style={styles.verticalTextContainer}>
            <Text style={styles.verticalText}>PRIMARY CONSULTANT</Text>
          </View>
          <View style={styles.signatureFields}>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>Signature:</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.consultantSignature}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, consultantSignature: text }))}
                />
            </View>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>Name:</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.consultantName}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, consultantName: text }))}
                />
            </View>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>Designation</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.consultantDesignation}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, consultantDesignation: text }))}
                />
            </View>
            <View style={styles.fieldRow}>
              <Text style={styles.label}>KMC No</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.consultantKmcNo}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, consultantKmcNo: text }))}
                />
            </View>
            <View style={styles.fieldRowDateAndTime}>
              <Text style={styles.label}>Date</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.consultantDate}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, consultantDate: text }))}
                />
              <Text style={styles.label}>Time</Text>
                <TextInput 
                  style={[styles.input, styles.underlineInput, { flex: 1 }]} 
                  value={formState.consultantTime}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, consultantTime: text }))}
                />
            </View>
          </View>
        </View>
      </View>
    </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 10,
  },
  header: {
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#000',
    padding: 5,
  },
  headerTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  logo: {
    width: 350,
    height: 100,
  },
  headerCenteredText: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 16,
  },
  section: {
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#000',
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    padding: 8,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  largeTextArea: {
    height: 150,
  },
  signatureSectionContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#000',
    flex: 1,
  },
  signatureColumn: {
    flex: 1,
    borderRightWidth: 1,
    borderColor: '#000',
    flexDirection: 'row',
  },
  verticalTextContainer: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    borderRightWidth: 1,
    borderColor: '#000',
  },
  verticalText: {
    transform: [{ rotate: '-90deg' }],
    fontSize: 16,
    fontWeight: 'bold',
  },
  signatureFields: {
    flex: 1,
    padding: 10,
  },
  fieldRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  fieldRowDateAndTime: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  label: {
    fontSize: 16,
    marginRight: 5,
    fontWeight: '500',
  },
  underlineInput: {
    borderBottomWidth: 1,
    borderColor: '#000',
    borderRadius: 0,
    paddingHorizontal: 0,
    paddingVertical: 2,
    backgroundColor: 'transparent',
  },
});

export default DiagnosisPage4; 