import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import React, { useEffect, useState } from 'react';
import {
  <PERSON>ert,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import DiagnosisPage1 from '../../diagnosis/DiagnosisPage1';
import DiagnosisPage2 from '../../diagnosis/DiagnosisPage2';
import DiagnosisPage3 from '../../diagnosis/DiagnosisPage3';
import DiagnosisPage4 from '../../diagnosis/DiagnosisPage4';
import { supabase } from '../utils/supabase';
import * as FileSystem from 'expo-file-system';
import Dialog from 'react-native-dialog';

const DiagnosisFormScreen = ({ route, navigation }) => {
  // Ensure mrno is properly initialized
  const { mrno } = route.params || {};
  
  // Add validation for mrno
  useEffect(() => {
    if (!mrno) {
      console.log('Warning: No patient ID provided');
      Alert.alert('Error', 'No patient ID provided. Please return to patient selection.');
      navigation.goBack();
      return;
    }
    // If this is a new appointment, clear the form data
    if (route.params && route.params.isNewAppointment) {
      setFormData({});
    }
  }, [mrno, navigation]);

  const initialTemplates = [
    { id: 'template-1', title: 'Patient Information & Diagnosis', component: DiagnosisPage1 },
    { id: 'template-2', title: 'Lab Results & Imaging', component: DiagnosisPage2 },
    { id: 'template-3', title: 'Medication & Follow-up', component: DiagnosisPage3 },
    { id: 'template-4', title: 'Emergency & Hospital Contacts', component: DiagnosisPage4 },
  ];

  const [pages, setPages] = useState(initialTemplates.map(template => ({
    instanceId: template.id,
    templateId: template.id,
    title: template.title,
    component: template.component,
    isTemplate: true,
  })));

  const [selectedInstanceId, setSelectedInstanceId] = useState(null);
  const [formData, setFormData] = useState({});

  const [isAddPageModalVisible, setAddPageModalVisible] = useState(false);
  const [templateInstanceCounts, setTemplateInstanceCounts] = useState({
    'template-1': 0,
    'template-2': 0,
    'template-3': 0,
    'template-4': 0,
  });

  const [currentPage, setCurrentPage] = useState(0);
  const [savedPDFs, setSavedPDFs] = useState([]);
  const [showSavedPDFs, setShowSavedPDFs] = useState(false);

  const [showLinkDialog, setShowLinkDialog] = React.useState(false);
  const [publicLink, setPublicLink] = React.useState('');
  const [pendingPDFUri, setPendingPDFUri] = React.useState(null);

  // Load saved form data when component mounts
  useEffect(() => {
    const loadSavedData = async () => {
      try {
        const savedData = await AsyncStorage.getItem('diagnosisFormData');
        if (savedData) {
          setFormData(JSON.parse(savedData));
        }
      } catch (error) {
        console.error('Error loading saved data:', error);
      }
    };
    loadSavedData();
  }, []);

  // Clear saved PDFs when patient changes
  useEffect(() => {
    if (!mrno) return;
    
    console.log('Loading PDFs for patient:', mrno);
    setSavedPDFs([]); // Clear existing PDFs
    loadSavedPDFs(); // Load new patient's PDFs
  }, [mrno]);

  const loadSavedPDFs = async () => {
    if (!mrno) return;

    try {
      const savedPDFsData = await AsyncStorage.getItem(`diagnosisFormPDFs_${mrno}`);
      if (savedPDFsData) {
        const parsedPDFs = JSON.parse(savedPDFsData);
        // Strictly filter PDFs for current patient
        const patientPDFs = parsedPDFs.filter(pdf => pdf.patientId === mrno);
        console.log('Loaded PDFs for patient:', patientPDFs.length);
        setSavedPDFs(patientPDFs);
      } else {
        setSavedPDFs([]);
      }
    } catch (error) {
      console.error('Error loading saved PDFs:', error);
      setSavedPDFs([]);
    }
  };

  const handlePageSelect = (instanceIdToSelect) => {
    const selectedPageItem = pages.find(p => p.instanceId === instanceIdToSelect);

    if (selectedPageItem.isTemplate) {
      const newCount = templateInstanceCounts[selectedPageItem.templateId] + 1;
      const newInstanceId = `${selectedPageItem.templateId}-instance-${newCount}`;
      const newInstanceTitle = `${selectedPageItem.title} - Instance ${newCount}`;

      const newInstance = {
        instanceId: newInstanceId,
        templateId: selectedPageItem.templateId,
        title: newInstanceTitle,
        component: selectedPageItem.component,
        isTemplate: false,
      };

      setPages(prevPages => [...prevPages, newInstance]);
      setFormData(prevFormData => ({
        ...prevFormData,
        [newInstanceId]: {},
      }));
      setTemplateInstanceCounts(prevCounts => ({
        ...prevCounts,
        [selectedPageItem.templateId]: newCount,
      }));
      setSelectedInstanceId(newInstanceId);
      setAddPageModalVisible(false);
    } else {
      setSelectedInstanceId(instanceIdToSelect);
    }
  };

  const handleFormChange = async (data) => {
    // If the data includes a PDF URI, save it
    if (data.pdfUri) {
      try {
        const timestamp = new Date().toISOString();
        const newPDF = {
          uri: data.pdfUri,
          formType: data.formType,
          timestamp,
          formData: { ...data },
          patientId: mrno
        };

        // Update saved PDFs
        const updatedPDFs = [...savedPDFs, newPDF];
        setSavedPDFs(updatedPDFs);

        // Save to AsyncStorage with patient ID
        await AsyncStorage.setItem(`diagnosisFormPDFs_${mrno}`, JSON.stringify(updatedPDFs));
      } catch (error) {
        console.error('Error saving PDF:', error);
      }
    }

    // Update form data
    setFormData(prevData => ({
      ...prevData,
      [currentPage]: data
    }));
  };

  const handleFormChangeForPage = (pageIndex, data) => {
    setFormData(prevData => ({
      ...prevData,
      [pageIndex]: data
    }));
  };

  const saveCurrentFormData = async (pageIndex) => {
    try {
      await AsyncStorage.setItem('diagnosisFormData', JSON.stringify(formData));
    } catch (error) {
      console.error('Error saving form data:', error);
    }
  };

  const handleSaveAllForms = async () => {
    try {
      console.log('Starting PDF combination process for patient:', mrno);
      
      // Get saved PDFs for this patient
      const savedPDFsData = await AsyncStorage.getItem(`diagnosisFormPDFs_${mrno}`);
      if (!savedPDFsData) {
        Alert.alert('Error', 'No saved PDFs found for this patient');
        return;
      }

      const savedPDFs = JSON.parse(savedPDFsData);
      // Double check that we only combine PDFs for current patient
      const patientPDFs = savedPDFs.filter(pdf => pdf.patientId === mrno);
      
      if (patientPDFs.length === 0) {
        Alert.alert('Error', 'No saved PDFs found for this patient');
        return;
      }

      console.log('Found saved PDFs for patient:', patientPDFs.length);

      // Create HTML content that embeds each PDF
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              @page {
                size: A4;
                margin: 0;
              }
              body { 
                margin: 0;
                padding: 0;
              }
              .pdf-container {
                page-break-after: always;
                height: 100vh;
                width: 100%;
              }
              .pdf-container:last-child {
                page-break-after: avoid;
              }
              iframe {
                width: 100%;
                height: 100%;
                border: none;
              }
            </style>
          </head>
          <body>
            ${patientPDFs.map(pdf => `
              <div class="pdf-container">
                <iframe src="${pdf.uri}"></iframe>
              </div>
            `).join('')}
          </body>
        </html>
      `;

      console.log('Generating combined PDF...');
      
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        width: 595,
        height: 842,
        base64: false
      });

      console.log('Combined PDF generated at:', uri);

      // Save the combined PDF
      const timestamp = new Date().toISOString();
      const newPDF = {
        uri: uri,
        formType: 'combined',
        timestamp,
        patientId: mrno
      };

      // Update saved PDFs list
      const updatedPDFs = [...patientPDFs, newPDF];
      setSavedPDFs(updatedPDFs);

      // Save to AsyncStorage
      await AsyncStorage.setItem(`diagnosisFormPDFs_${mrno}`, JSON.stringify(updatedPDFs));

      Alert.alert(
        'Success',
        'All forms have been combined into a single PDF!',
        [
          {
            text: 'View PDF',
            onPress: () => handleViewPDF(newPDF)
          },
          {
            text: 'OK',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('Error combining PDFs:', error);
      Alert.alert('Error', 'Failed to combine PDFs');
    }
  };

  const handleIndividualFormSave = async () => {
    const currentPage = pages.find(p => p.instanceId === selectedInstanceId);
    if (currentPage) {
      // Create a new array with the updated page
      const updatedPages = pages.map(page => 
        page.instanceId === selectedInstanceId 
          ? { ...page, title: `${page.title} (Saved)` }
          : page
      );

      // Update pages state with the new array
      setPages(updatedPages);
      
      // Save all form data to AsyncStorage
      try {
        await AsyncStorage.setItem('diagnosisFormData', JSON.stringify(formData));
        
        // Show success message
        Alert.alert(
          'Success',
          'Form data saved successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                setSelectedInstanceId(null);
                setPages(prevPages => [...prevPages]);
              }
            }
          ]
        );
      } catch (error) {
        Alert.alert('Error', 'Failed to save form data');
      }
    }
  };

  const handleViewPDF = async (pdf) => {
    try {
      // Only allow viewing PDFs for current patient
      if (pdf.patientId !== mrno) {
        Alert.alert('Error', 'Cannot view PDFs from other patients');
        return;
      }

      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(pdf.uri, {
          mimeType: 'application/pdf',
          dialogTitle: `View ${pdf.formType === 'combined' ? 'Combined Forms' : 'Form ' + pdf.formType}`,
          UTI: 'com.adobe.pdf'
        });
      } else {
        Alert.alert('Error', 'Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Error viewing PDF:', error);
      Alert.alert('Error', 'Failed to view PDF');
    }
  };

  const handleDeletePDF = async (pdfToDelete) => {
    try {
      // Filter out the PDF to delete
      const updatedPDFs = savedPDFs.filter(pdf => 
        pdf.patientId === mrno && pdf.timestamp !== pdfToDelete.timestamp
      );
      
      // Update state and storage
      setSavedPDFs(updatedPDFs);
      await AsyncStorage.setItem(`diagnosisFormPDFs_${mrno}`, JSON.stringify(updatedPDFs));
      
      Alert.alert('Success', 'PDF deleted successfully');
    } catch (error) {
      console.error('Error deleting PDF:', error);
      Alert.alert('Error', 'Failed to delete PDF');
    }
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const renderSavedPDFs = () => {
    // Filter PDFs for current patient only
    const patientPDFs = savedPDFs.filter(pdf => pdf.patientId === mrno);

    if (patientPDFs.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No saved PDFs for this patient</Text>
        </View>
      );
    }

    return (
      <ScrollView style={styles.savedPDFsContainer}>
        {patientPDFs.map((pdf, index) => (
          <View key={index} style={styles.pdfItem}>
            <View style={styles.pdfInfo}>
              <Text style={styles.pdfTitle}>
                {pdf.formType === 'combined' ? 'Combined Forms' : `Form ${pdf.formType}`}
              </Text>
              <Text style={styles.pdfTimestamp}>
                {new Date(pdf.timestamp).toLocaleString()}
              </Text>
            </View>
            <View style={styles.pdfActions}>
              <TouchableOpacity
                style={[styles.pdfButton, styles.viewButton]}
                onPress={() => handleViewPDF(pdf)}
              >
                <Text style={styles.pdfButtonText}>View</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.pdfButton, styles.deleteButton]}
                onPress={() => handleDeletePDF(pdf)}
              >
                <Text style={styles.pdfButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    );
  };

  const renderPageList = () => (
    <View style={styles.container}>
      <Text style={styles.title}>Diagnosis Forms</Text>
      <ScrollView style={styles.pageList}>
        {pages.map((page) => (
          <TouchableOpacity
            key={page.instanceId}
            style={[
              styles.pageButton,
              page.title.includes('(Saved)') && styles.savedPageButton
            ]}
            onPress={() => handlePageSelect(page.instanceId)}
          >
            <Text style={styles.pageButtonText}>{page.title}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      <TouchableOpacity style={styles.addPageButton} onPress={() => setAddPageModalVisible(true)}>
        <Text style={styles.addPageButtonText}>Add New Page Instance</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.saveButton} onPress={handleSaveAllForms}>
        <Text style={styles.saveButtonText}>Save All Forms</Text>
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={isAddPageModalVisible}
        onRequestClose={() => setAddPageModalVisible(false)}
      >
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text style={styles.modalTitle}>Select a Template Page to Add</Text>
            {initialTemplates.map(template => (
              <TouchableOpacity
                key={template.id}
                style={styles.modalButton}
                onPress={() => handlePageSelect(template.id)}
              >
                <Text style={styles.modalButtonText}>{template.title}</Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={[styles.modalButton, styles.modalCloseButton]}
              onPress={() => setAddPageModalVisible(false)}
            >
              <Text style={styles.modalButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );

  const renderDiagnosisPage = () => {
    if (!selectedInstanceId) {
      return null;
    }

    const currentPage = pages.find(p => p.instanceId === selectedInstanceId);
    if (!currentPage) {
      return null;
    }

    const PageComponent = currentPage.component;

    const handlePageFormDataChange = (pageData) => {
      setFormData(prevFormData => ({
        ...prevFormData,
        [selectedInstanceId]: pageData,
      }));
    };

    return (
      <ScrollView style={styles.formView}>
        <PageComponent
          instanceId={selectedInstanceId}
          onFormDataChange={handlePageFormDataChange}
          mrno={mrno}
        />
        <TouchableOpacity style={styles.saveButton} onPress={handleIndividualFormSave}>
          <Text style={styles.saveButtonText}>Save Form</Text>
        </TouchableOpacity>
      </ScrollView>
    );
  };

  const resetFormData = () => {
    // Reset form data for all pages
    const emptyFormData = {
      // Page 1
      presentingComplaints: '',
      historyOfComplaints: '',
      pastMedicalHistory: '',
      regularMedication: '',
      
      // Page 2
      familyHistory: '',
      socialHistory: '',
      occupationalHistory: '',
      allergies: '',
      generalPhysicalExamination: {
        conscious: false,
        oriented: false,
        cooperative: false,
        wellBuilt: false,
        wellNourished: false,
        afebrile: false,
        noPallor: false,
        noIcterus: false,
        noCyanosis: false,
        noClubbing: false,
        noLymphadenopathy: false,
        noEdema: false,
        noDehydration: false
      },
      vitals: {
        pulse: '',
        bp: '',
        spo2: '',
        temp: '',
        rr: '',
        weight: '',
        height: '',
        bmi: ''
      },
      systemicExamination: '',
      investigationDone: '',
      
      // Page 3
      procedures: '',
      additionalInformation: '',
      planOfManagement: '',
      
      // Page 4
      provisionalDiagnosis: '',
      additionalInfo: '',
      doctorSignature: '',
      doctorName: '',
      doctorDesignation: '',
      doctorKmcNo: '',
      doctorDateTime: '',
      primaryConsultantSignature: '',
      primaryConsultantName: '',
      primaryConsultantDesignation: '',
      primaryConsultantKmcNo: '',
      primaryConsultantDateTime: ''
    };

    setFormData(emptyFormData);
  };

  const handleReset = async () => {
    Alert.alert(
      'Reset All Forms',
      'Are you sure you want to delete all saved forms and PDFs? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Get all keys from AsyncStorage
              const keys = await AsyncStorage.getAllKeys();
              
              // Filter keys related to this patient's diagnosis forms and PDFs
              const patientKeys = keys.filter(key => 
                key.startsWith(`diagnosisFormPDFs_${mrno}`) ||
                key.startsWith(`diagnosisPage1Data_${mrno}`) ||
                key.startsWith(`diagnosisPage2Data_${mrno}`) ||
                key.startsWith(`diagnosisPage3Data_${mrno}`) ||
                key.startsWith(`diagnosisPage4Data_${mrno}`)
              );
              
              // Remove all patient-related data
              await Promise.all(patientKeys.map(key => AsyncStorage.removeItem(key)));
              
              // Reset form data
              resetFormData();
              
              // Clear saved PDFs
              setSavedPDFs([]);
              
              // Reset current page
              setCurrentPage(0);
              
              Alert.alert('Success', 'All forms and PDFs have been reset');
            } catch (error) {
              console.error('Error resetting forms:', error);
              Alert.alert('Error', 'Failed to reset forms');
            }
          }
        }
      ]
    );
  };

  const handleSaveAllFormsCombinedPDF = async () => {
    try {
      // Generate a single PDF from all 4 pages' form data
      const allData = formData;
      const htmlContent = `
        <html><head><meta charset="utf-8"><style>@page { size: A4; margin: 0; } body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #000; } .page { margin: 20px 0; padding: 20px; border: 1px solid #000; background: white; min-height: 800px; } .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #000; padding-bottom: 10px; } .header-title { font-size: 20px; font-weight: bold; margin: 0; } .header-subtitle { font-size: 16px; margin: 5px 0; } .section { margin-bottom: 20px; border: 1px solid #000; } .section-header { background-color: #f0f0f0; padding: 8px; border-bottom: 1px solid #000; } .section-title { font-size: 16px; font-weight: bold; margin: 0; } .input-area { padding: 8px; min-height: 150px; white-space: pre-wrap; }</style></head><body>
          <div class="page">
            <div class="header">
              <h1 class="header-title">CURA HOSPITALS</h1>
              <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
              <h2 class="header-subtitle">FORM(1)</h2>
            </div>
            <div class="section"><div class="section-header"><h3 class="section-title">Presenting Complaints</h3></div><div class="input-area">${allData[0]?.presentingComplaints || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">History of Presenting Complaints</h3></div><div class="input-area">${allData[0]?.historyOfComplaints || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Past Medical / Surgical History</h3></div><div class="input-area">${allData[0]?.pastMedicalHistory || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Regular Medication</h3></div><div class="input-area">${allData[0]?.regularMedication || ''}</div></div>
          </div>
          <div class="page">
            <div class="header">
              <h1 class="header-title">CURA HOSPITALS</h1>
              <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
              <h2 class="header-subtitle">FORM(2)</h2>
            </div>
            <div class="section"><div class="section-header"><h3 class="section-title">Family/Social/Occupational History</h3></div><div class="input-area">${allData[1]?.familyHistory || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Allergies</h3></div><div class="input-area">${allData[1]?.allergies || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">General Physical Examination</h3></div><div class="input-area">Pallor: ${allData[1]?.pallorChecked ? 'Yes' : 'No'}, Icterus: ${allData[1]?.icterusChecked ? 'Yes' : 'No'}, Cyanosis: ${allData[1]?.cyanosisChecked ? 'Yes' : 'No'}, Clubbing: ${allData[1]?.clubbingChecked ? 'Yes' : 'No'}, Lymphadenopathy: ${allData[1]?.lymphadenopathyChecked ? 'Yes' : 'No'}, Pedal Edema: ${allData[1]?.pedalEdemaChecked ? 'Yes' : 'No'}, Others: ${allData[1]?.others || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Vitals</h3></div><div class="input-area">HR: ${allData[1]?.hr || ''}, BP: ${allData[1]?.bp || ''}, RR: ${allData[1]?.rr || ''}, SpO2: ${allData[1]?.spo2 || ''}, Temp: ${allData[1]?.temp || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Systemic Examination</h3></div><div class="input-area">Airway: ${allData[1]?.airway || ''}, RS: ${allData[1]?.rs || ''}, CVS: ${allData[1]?.cvs || ''}, P/A: ${allData[1]?.pa || ''}, CNS: ${allData[1]?.cns || ''}, Pain: ${allData[1]?.pain || ''}, Local Examination: ${allData[1]?.localExamination || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Investigation Done</h3></div><div class="input-area">${allData[1]?.investigationDone || ''}</div></div>
          </div>
          <div class="page">
            <div class="header">
              <h1 class="header-title">CURA HOSPITALS</h1>
              <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
              <h2 class="header-subtitle">FORM(3)</h2>
            </div>
            <div class="section"><div class="section-header"><h3 class="section-title">Procedure(s)</h3></div><div class="input-area">${allData[2]?.procedures || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Additional Information</h3></div><div class="input-area">${allData[2]?.additionalInfo || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Plan of Management</h3></div><div class="input-area">${allData[2]?.planOfManagement || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">As Ordered by Dr</h3></div><div class="input-area">${allData[2]?.doctorName || ''}, Speciality: ${allData[2]?.speciality || ''}</div></div>
          </div>
          <div class="page">
            <div class="header">
              <h1 class="header-title">CURA HOSPITALS</h1>
              <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
              <h2 class="header-subtitle">FORM(4)</h2>
            </div>
            <div class="section"><div class="section-header"><h3 class="section-title">Provisional Diagnosis</h3></div><div class="input-area">${allData[3]?.provisionalDiagnosis || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Additional Information</h3></div><div class="input-area">${allData[3]?.additionalInfo || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Doctor</h3></div><div class="input-area">Signature: ${allData[3]?.doctorSignature || ''}, Name: ${allData[3]?.doctorName || ''}, Designation: ${allData[3]?.doctorDesignation || ''}, KMC No: ${allData[3]?.doctorKmcNo || ''}, Date: ${allData[3]?.doctorDate || ''}, Time: ${allData[3]?.doctorTime || ''}</div></div>
            <div class="section"><div class="section-header"><h3 class="section-title">Primary Consultant</h3></div><div class="input-area">Signature: ${allData[3]?.consultantSignature || ''}, Name: ${allData[3]?.consultantName || ''}, Designation: ${allData[3]?.consultantDesignation || ''}, KMC No: ${allData[3]?.consultantKmcNo || ''}, Date: ${allData[3]?.consultantDate || ''}, Time: ${allData[3]?.consultantTime || ''}</div></div>
          </div>
        </body></html>
      `;
      const { uri } = await Print.printToFileAsync({ html: htmlContent, width: 595, height: 842, base64: false });

      // Show the share dialog
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Save or Share Diagnosis PDF',
          UTI: 'com.adobe.pdf',
        });
      } else {
        Alert.alert('Error', 'Sharing is not available on this device');
        return;
      }
      // Prompt for the public link
      setPendingPDFUri(uri);
      setShowLinkDialog(true);
    } catch (error) {
      console.error('Error generating combined PDF:', error);
      Alert.alert('Error', 'Failed to generate combined PDF');
    }
  };

  // Handler for saving the link
  const handleSavePublicLink = async () => {
    setShowLinkDialog(false);
    if (!publicLink) return;
    if (mrno && publicLink) {
      // Fetch latest appointment_id for this mrno
      const { data: latest, error: fetchError } = await supabase
        .from('appointments')
        .select('appointment_id')
        .eq('mrno', mrno)
        .order('date', { ascending: false })
        .order('time', { ascending: false })
        .limit(1)
        .single();
      if (!fetchError && latest && latest.appointment_id) {
        const { error: updateError } = await supabase
          .from('appointments')
          .update({ prescription_file_path: publicLink })
          .eq('appointment_id', latest.appointment_id);
        if (!updateError) {
          Alert.alert('Success', 'Link saved in database!', [
            { text: 'OK', onPress: async () => {
              // Clear cache and redirect as before
              try {
                await AsyncStorage.removeItem('diagnosisFormData');
                await AsyncStorage.removeItem(`diagnosisFormPDFs_${mrno}`);
                await AsyncStorage.removeItem(`diagnosisPage1Data_${mrno}`);
                await AsyncStorage.removeItem(`diagnosisPage2Data_${mrno}`);
                await AsyncStorage.removeItem(`diagnosisPage3Data_${mrno}`);
                await AsyncStorage.removeItem(`diagnosisPage4Data_${mrno}`);
              } catch (e) {}
              try {
                const { data: patient } = await supabase
                  .from('users')
                  .select('*')
                  .eq('mrno', mrno)
                  .single();
                const { data: appointments } = await supabase
                  .from('appointments')
                  .select('*')
                  .eq('mrno', mrno)
                  .order('date', { ascending: false })
                  .order('time', { ascending: false });
                navigation.navigate('PatientDetails', { patient, appointments });
              } catch (e) {
                navigation.navigate('PatientDetails', { patient: { mrno }, appointments: [] });
              }
            }}
          ]);
        } else {
          Alert.alert('Error', 'Failed to update appointment with link.');
        }
      }
    }
    setPublicLink('');
    setPendingPDFUri(null);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Diagnosis Forms</Text>
      </View>

      {showSavedPDFs ? (
        renderSavedPDFs()
      ) : (
        <View style={styles.content}>
          {currentPage === 0 && (
            <DiagnosisPage1
              formData={formData[0]}
              onChange={data => handleFormChangeForPage(0, data)}
              mrno={mrno}
            />
          )}
          {currentPage === 1 && (
            <DiagnosisPage2
              formData={formData[1]}
              onChange={data => handleFormChangeForPage(1, data)}
              mrno={mrno}
            />
          )}
          {currentPage === 2 && (
            <DiagnosisPage3
              formData={formData[2]}
              onChange={data => handleFormChangeForPage(2, data)}
              mrno={mrno}
            />
          )}
          {currentPage === 3 && (
            <DiagnosisPage4
              formData={formData[3]}
              onChange={data => handleFormChangeForPage(3, data)}
              mrno={mrno}
            />
          )}
        </View>
      )}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.navButton]}
          onPress={() => setCurrentPage(prev => Math.max(0, prev - 1))}
          disabled={currentPage === 0}
        >
          <Text style={styles.buttonText}>Previous</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.navButton]}
          onPress={async () => {
            // Save current form data before moving to next page
            await saveCurrentFormData(currentPage);
            setCurrentPage(prev => Math.min(3, prev + 1));
          }}
          disabled={currentPage === 3}
        >
          <Text style={styles.buttonText}>Next</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.saveButton]}
          onPress={handleSaveAllFormsCombinedPDF}
        >
          <Text style={styles.buttonText}>Save All Forms</Text>
        </TouchableOpacity>
      </View>

      <Dialog.Container visible={showLinkDialog}>
        <Dialog.Title>Paste Public Link</Dialog.Title>
        <Dialog.Description>
          After saving the PDF to your cloud storage, please paste the public link here.
        </Dialog.Description>
        <Dialog.Input
          placeholder="Paste link here"
          value={publicLink}
          onChangeText={setPublicLink}
        />
        <Dialog.Button label="Cancel" onPress={() => { setShowLinkDialog(false); setPublicLink(''); setPendingPDFUri(null); }} />
        <Dialog.Button label="Save Link" onPress={handleSavePublicLink} />
      </Dialog.Container>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  pageList: {
    flex: 1,
  },
  pageButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  pageButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  addPageButton: {
    backgroundColor: '#FF9500',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  addPageButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#34C759',
    padding: 15,
    borderRadius: 10,
    marginTop: 10,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  pageContainer: {
    flex: 1,
  },
  pageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    color: '#007AFF',
    fontSize: 16,
  },
  pageTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  pageContent: {
    flex: 1,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '80%',
  },
  modalTitle: {
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalButton: {
    backgroundColor: '#007AFF',
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginTop: 10,
    width: '100%',
  },
  modalButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalCloseButton: {
    backgroundColor: '#E0E0E0',
    marginTop: 20,
  },
  savedPageButton: {
    backgroundColor: '#34C759', // Green color for saved pages
  },
  formView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  savedPDFsContainer: {
    flex: 1,
    padding: 15,
  },
  pdfItem: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  pdfInfo: {
    marginBottom: 10,
  },
  pdfTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  pdfTimestamp: {
    fontSize: 14,
    color: '#6c757d',
  },
  pdfActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  pdfButton: {
    padding: 8,
    borderRadius: 5,
    marginLeft: 10,
    minWidth: 80,
    alignItems: 'center',
  },
  viewButton: {
    backgroundColor: '#007bff',
  },
  deleteButton: {
    backgroundColor: '#dc3545',
  },
  pdfButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#6c757d',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  navButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
  },
});

export default DiagnosisFormScreen;