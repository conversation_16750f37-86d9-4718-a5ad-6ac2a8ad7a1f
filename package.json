{"dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/supabase-js": "^1.35.7", "expo": "^53.0.4", "expo-asset": "^11.1.5", "expo-barcode-scanner": "^13.0.1", "expo-camera": "~16.1.6", "expo-file-system": "~18.1.10", "expo-image-picker": "~16.1.4", "expo-linking": "^7.1.5", "expo-print": "~14.1.4", "expo-sharing": "~13.1.5", "jsqr": "^1.4.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-camera": "^4.2.1", "react-native-dialog": "^9.3.0", "react-native-elements": "^3.4.3", "react-native-html-to-pdf": "^0.12.0", "react-native-pdf-lib": "^1.0.0", "react-native-polyfill-globals": "^3.1.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-signature-canvas": "^4.7.4", "react-native-url-polyfill": "^2.0.0", "react-native-view-shot": "^4.0.3", "react-native-web": "^0.20.0", "stream-browserify": "^3.0.0", "web-streams-polyfill": "^4.1.0", "expo-linear-gradient": "~14.1.5", "expo-blur": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.19.3"}, "name": "hospital-opd-app", "version": "1.0.0", "private": true, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}}