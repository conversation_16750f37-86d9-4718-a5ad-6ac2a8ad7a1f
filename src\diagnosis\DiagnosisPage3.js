import React from 'react';
import { Image, StyleSheet, Text, TextInput, View } from 'react-native';

const DiagnosisPage3 = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/Logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>DIAGNOSIS FORM (3)</Text>
        </View>
      </View>

      {/* Medication Prescription Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Medication Prescription</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Prescribed Medications:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter prescribed medications"
              multiline
              numberOfLines={4}
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Dosage Instructions:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter dosage instructions"
              multiline
              numberOfLines={4}
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Duration:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter medication duration"
              multiline
              numberOfLines={4}
            />
          </View>
        </View>
      </View>

      {/* Follow-up Instructions Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Follow-up Instructions</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Follow-up Date:</Text>
            <TextInput
              style={styles.input}
              placeholder="DD/MM/YYYY"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Instructions:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter follow-up instructions"
              multiline
              numberOfLines={6}
            />
          </View>
        </View>
      </View>

      {/* Doctor's Signature Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Doctor's Signature</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Doctor's Name:</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter doctor's name"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Signature:</Text>
            <View style={styles.signatureBox} />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
    paddingBottom: 10,
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 150,
    height: 60,
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    paddingLeft: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  section: {
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#000',
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  sectionContent: {
    padding: 5,
  },
  fieldContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  signatureBox: {
    height: 100,
    borderWidth: 1,
    borderColor: '#ccc',
    borderStyle: 'dashed',
    borderRadius: 4,
  },
});

export default DiagnosisPage3; 