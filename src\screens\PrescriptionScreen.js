import React, { useEffect, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../utils/supabase';

const FREQUENCY_OPTIONS = [
  { label: 'Once a day (OD)', value: 1 },
  { label: 'Twice a day (BD)', value: 2 },
  { label: 'Thrice a day (TDS)', value: 3 },
  { label: 'Custom', value: 'custom' },
];
const TIME_OF_DAY_OPTIONS = [
  'Before Breakfast',
  'After Breakfast',
  'Before Lunch',
  'After Lunch',
  'Before Dinner',
  'After Dinner',
];

const PrescriptionScreen = ({ route, navigation }) => {
  const { appointment_id } = route.params;
  const [loading, setLoading] = useState(true);
  const [medicines, setMedicines] = useState([
    { name: '', dosage: '', frequency: 1, customFrequency: '', timesOfDay: [] }
  ]);

  useEffect(() => {
    const fetchPrescription = async () => {
      const { data, error } = await supabase
        .from('appointments')
        .select('prescription')
        .eq('appointment_id', appointment_id)
        .single();
      if (data && data.prescription && Array.isArray(data.prescription)) {
        setMedicines(data.prescription);
      }
      setLoading(false);
    };
    fetchPrescription();
  }, [appointment_id]);

  const handleMedicineChange = (index, field, value) => {
    setMedicines(prev => prev.map((med, i) => i === index ? { ...med, [field]: value } : med));
  };

  const handleFrequencyChange = (index, value) => {
    setMedicines(prev => prev.map((med, i) => i === index ? { ...med, frequency: value, customFrequency: '' } : med));
  };

  const handleTimeOfDayToggle = (index, option) => {
    setMedicines(prev => prev.map((med, i) => {
      if (i !== index) return med;
      const timesOfDay = med.timesOfDay.includes(option)
        ? med.timesOfDay.filter(t => t !== option)
        : [...med.timesOfDay, option];
      return { ...med, timesOfDay };
    }));
  };

  const handleAddMedicine = () => {
    setMedicines(prev => ([...prev, { name: '', dosage: '', frequency: 1, customFrequency: '', timesOfDay: [] }]));
  };

  const handleSave = async () => {
    if (medicines.some(med => !med.name || !med.dosage)) {
      Alert.alert('Error', 'Please fill in medicine name and dosage for all medicines.');
      return;
    }
    const saveObj = medicines.map(med => ({
      ...med,
      frequency: med.frequency === 'custom' ? med.customFrequency : med.frequency
    }));
    const { error } = await supabase
      .from('appointments')
      .update({ prescription: saveObj })
      .eq('appointment_id', appointment_id);
    if (!error) {
      Alert.alert('Success', 'Prescription saved!');
      navigation.goBack();
    } else {
      Alert.alert('Error', 'Failed to save prescription.');
    }
  };

  if (loading) {
    return <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><Text>Loading...</Text></View>;
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>Prescription</Text>
      {medicines.map((med, idx) => (
        <View key={idx} style={[styles.section, { borderWidth: 1, borderColor: '#eee', borderRadius: 8, marginBottom: 20, padding: 10 }]}> 
          <Text style={styles.label}>Medicine Name</Text>
          <TextInput
            style={styles.input}
            value={med.name}
            onChangeText={text => handleMedicineChange(idx, 'name', text)}
            placeholder="Enter medicine name"
          />
          <Text style={styles.label}>Dosage</Text>
          <TextInput
            style={styles.input}
            value={med.dosage}
            onChangeText={text => handleMedicineChange(idx, 'dosage', text)}
            placeholder="Enter dosage"
          />
          <Text style={styles.label}>Frequency per Day</Text>
          {FREQUENCY_OPTIONS.map(opt => (
            <TouchableOpacity
              key={opt.value}
              style={styles.checkboxRow}
              onPress={() => handleFrequencyChange(idx, opt.value)}
            >
              <View style={[styles.checkbox, med.frequency === opt.value && styles.checkboxChecked]} />
              <Text style={styles.checkboxLabel}>{opt.label}</Text>
            </TouchableOpacity>
          ))}
          {med.frequency === 'custom' && (
            <TextInput
              style={styles.input}
              value={med.customFrequency}
              onChangeText={text => handleMedicineChange(idx, 'customFrequency', text)}
              placeholder="Enter custom times per day"
              keyboardType="numeric"
            />
          )}
          <Text style={styles.label}>Time of Day</Text>
          {TIME_OF_DAY_OPTIONS.map(option => (
            <TouchableOpacity
              key={option}
              style={styles.checkboxRow}
              onPress={() => handleTimeOfDayToggle(idx, option)}
            >
              <View style={[styles.checkbox, med.timesOfDay && med.timesOfDay.includes(option) && styles.checkboxChecked]} />
              <Text style={styles.checkboxLabel}>{option}</Text>
            </TouchableOpacity>
          ))}
        </View>
      ))}
      <TouchableOpacity
        style={[styles.saveButton, { backgroundColor: '#34A853', marginBottom: 16 }]}
        onPress={handleAddMedicine}
      >
        <Text style={styles.saveButtonText}>Add Medicine</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.saveButton, { backgroundColor: '#007AFF' }]}
        onPress={handleSave}
      >
        <Text style={styles.saveButtonText}>Save Prescription</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#fff',
    flexGrow: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    padding: 10,
    fontSize: 16,
    marginBottom: 12,
    backgroundColor: '#f9f9f9',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 4,
    marginRight: 10,
    backgroundColor: '#fff',
  },
  checkboxChecked: {
    backgroundColor: '#007AFF',
  },
  checkboxLabel: {
    fontSize: 15,
  },
  saveButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default PrescriptionScreen; 