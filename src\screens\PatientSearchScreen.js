import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Animated,
  Platform,
  Keyboard,
  Dimensions,
} from 'react-native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { supabase } from '../utils/supabase';
import { COLORS } from '../theme';
import { Easing } from 'react-native';

const { width } = Dimensions.get('window');

const PatientSearchScreen = ({ navigation }) => {
  const [query, setQuery] = useState('');
  const [allPatients, setAllPatients] = useState([]);
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchFocused, setSearchFocused] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    // Animate on mount
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      })
    ]).start();

    const fetchPatients = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('appointments')
          .select('patient_name, date, time, status, mrno')
          .order('date', { ascending: false })
          .order('time', { ascending: false })
          .neq('patient_name', null);
        
        if (error) throw error;
        
        // Remove duplicates by MR No, keeping the most recent appointment
        const uniquePatients = [];
        const mrnoSet = new Set();
        
        data.forEach(patient => {
          if (patient.mrno && !mrnoSet.has(patient.mrno)) {
            mrnoSet.add(patient.mrno);
            uniquePatients.push(patient);
          } else if (!patient.mrno) {
            // Handle cases where MR No is missing
            uniquePatients.push(patient);
          }
        });
        
        setAllPatients(uniquePatients);
        setResults(uniquePatients);
      } catch (e) {
        console.error('Error fetching patients:', e);
        Alert.alert('Error', 'Failed to load patients. Please try again.');
        setAllPatients([]);
        setResults([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPatients();
  }, []);

  const handleSearch = (searchText) => {
    setQuery(searchText);
    if (!searchText.trim()) {
      setResults(allPatients);
      return;
    }
    const q = searchText.trim().toLowerCase();
    const filtered = allPatients.filter(
      p => p.patient_name && p.patient_name.toLowerCase().includes(q)
    );
    setResults(filtered);
  };

  const clearSearch = () => {
    setQuery('');
    setResults(allPatients);
    Keyboard.dismiss();
  };

  const handleSelect = async (appointment) => {
    const mrno = appointment.mrno;
    if (!mrno) {
      Alert.alert('Error', 'No MR No found for this appointment.');
      return;
    }
    let patient = null;
    let appointments = [];
    // Fetch patient details from users table
    const { data: userData } = await supabase
      .from('users')
      .select('*')
      .eq('mrno', mrno)
      .maybeSingle();
    patient = userData;
    // Fetch all appointments for this patient
    const { data: appts } = await supabase
      .from('appointments')
      .select('*')
      .eq('mrno', mrno)
      .order('date', { ascending: false })
      .order('time', { ascending: false });
    appointments = appts || [];
    navigation.navigate('PatientDetails', { patient, appointments });
  };

  const renderItem = ({ item, index }) => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{
          translateY: fadeAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [50, 0],
          })
        }]
      }}
    >
      <TouchableOpacity 
        style={styles.resultCard} 
        onPress={() => handleSelect(item)}
        activeOpacity={0.9}
      >
        <View style={styles.patientInfo}>
          <View style={styles.avatarContainer}>
            <Ionicons name="person" size={28} color={COLORS.primary} />
          </View>
          <View style={styles.patientDetails}>
            <Text style={styles.resultName} numberOfLines={1} ellipsizeMode="tail">
              {item.patient_name || 'Unknown Patient'}
            </Text>
            <View style={styles.metaContainer}>
              <View style={styles.metaItem}>
                <Ionicons name="calendar-outline" size={14} color={COLORS.textLight} />
                <Text style={styles.metaText}>{item.date || 'N/A'}</Text>
              </View>
              <View style={styles.metaItem}>
                <Ionicons name="time-outline" size={14} color={COLORS.textLight} />
                <Text style={styles.metaText}>{item.time || 'N/A'}</Text>
              </View>
            </View>
            {item.reason && (
              <Text style={styles.reasonText} numberOfLines={1} ellipsizeMode="tail">
                {item.reason}
              </Text>
            )}
          </View>
        </View>
        <View style={[
          styles.statusBadge, 
          item.status === 'completed' ? styles.completedBadge : styles.pendingBadge
        ]}>
          <Text style={item.status === 'completed' ? styles.completedText : styles.pendingText}>
            {item.status === 'completed' ? 'Completed' : 'Pending'}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="search" size={60} color={COLORS.primaryLight} />
      <Text style={styles.emptyTitle}>No patients found</Text>
      <Text style={styles.emptyText}>
        {query ? 'Try a different search term' : 'No patients in the system yet'}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Search Patients</Text>
      </View>

      <Animated.View 
        style={[
          styles.searchContainer,
          { 
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }] 
          }
        ]}
      >
        <View style={[
          styles.searchBar,
          searchFocused && styles.searchBarFocused
        ]}>
          <Ionicons 
            name="search" 
            size={20} 
            color={searchFocused ? COLORS.primary : COLORS.textLight} 
            style={styles.searchIcon} 
          />
          <TextInput
            style={styles.input}
            placeholder="Search by patient name..."
            placeholderTextColor={COLORS.textLight}
            value={query}
            onChangeText={handleSearch}
            onFocus={() => setSearchFocused(true)}
            onBlur={() => setSearchFocused(false)}
            returnKeyType="search"
            autoCapitalize="words"
            clearButtonMode="while-editing"
          />
          {query.length > 0 && (
            <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
              <Ionicons name="close-circle" size={20} color={COLORS.textLight} />
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      ) : (
        <FlatList
          data={results}
          keyExtractor={(item, index) => `${item.mrno || 'unknown'}-${index}`}
          renderItem={renderItem}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyComponent}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="handled"
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingTop: 12,
    paddingBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    elevation: 4,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    marginLeft: 8,
  },
  searchContainer: {
    padding: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    paddingHorizontal: 16,
    height: 56,
    borderWidth: 1.5,
    borderColor: COLORS.primaryLight,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  searchBarFocused: {
    borderColor: COLORS.primary,
  },
  searchIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    height: '100%',
    fontSize: 16,
    color: COLORS.text,
    paddingVertical: 0,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
    paddingBottom: 24,
  },
  resultCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.1)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
  },
  patientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 12,
  },
  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(150, 120, 211, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  patientDetails: {
    flex: 1,
    marginRight: 8,
  },
  resultName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  metaContainer: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  metaText: {
    fontSize: 13,
    color: COLORS.textLight,
    marginLeft: 4,
  },
  reasonText: {
    fontSize: 13,
    color: COLORS.textLight,
    fontStyle: 'italic',
  },
  statusBadge: {
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 10,
    alignSelf: 'flex-start',
  },
  completedBadge: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  pendingBadge: {
    backgroundColor: 'rgba(251, 191, 36, 0.1)',
  },
  completedText: {
    color: COLORS.success,
    fontSize: 12,
    fontWeight: '600',
  },
  pendingText: {
    color: COLORS.warning,
    fontSize: 12,
    fontWeight: '600',
  },
  separator: {
    height: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 80,
  },
  loadingText: {
    marginTop: 16,
    color: COLORS.textLight,
    fontSize: 15,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    paddingTop: 80,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 15,
    color: COLORS.textLight,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default PatientSearchScreen;