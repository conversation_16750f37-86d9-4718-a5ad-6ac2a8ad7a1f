import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import React, { useEffect, useState } from 'react';
import { Alert, Image, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const DiagnosisPage3 = ({ formData, onChange, mrno }) => {
  const [formState, setFormState] = useState({
    procedures: '',
    additionalInfo: '',
    planOfManagement: '',
    doctorName: '',
    speciality: ''
  });

  // Load saved data when component mounts
  useEffect(() => {
    const loadSavedData = async () => {
      try {
        const keys = await AsyncStorage.getAllKeys();
        const savedKeys = keys.filter(key => key.startsWith(`diagnosisPage3Data_${mrno}_`));
        if (savedKeys.length > 0) {
          savedKeys.sort().reverse();
          const mostRecentKey = savedKeys[0];
          const savedData = await AsyncStorage.getItem(mostRecentKey);
          if (savedData) {
            setFormState(JSON.parse(savedData));
          }
        } else if (formData) {
          setFormState(formData);
        }
      } catch (error) {
        console.error('Error loading saved data:', error);
      }
    };
    loadSavedData();
  }, [formData, mrno]);

  // Update parent component whenever form state changes
  useEffect(() => {
    if (onChange) {
      onChange(formState);
    }
  }, [formState]);

  const handleSave = async () => {
    try {
      const timestamp = new Date().toISOString();
      const key = `diagnosisPage3Data_${mrno}_${timestamp}`;
      await AsyncStorage.setItem(key, JSON.stringify(formState));
      Alert.alert('Success', 'Diagnosis Page 3 data saved permanently!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save data');
    }
  };

  const handleSaveForm = async () => {
    try {
      console.log('Starting PDF generation...');
      
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              @page {
                size: A4;
                margin: 0;
              }
              body { 
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #000;
              }
              .page { 
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #000;
                background: white;
                min-height: 800px;
              }
              .header { 
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
              }
              .header-top-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
              }
              .logo {
                width: 350px;
                height: 100px;
                object-fit: contain;
              }
              .header-title {
                font-size: 20px;
                font-weight: bold;
                margin: 0;
              }
              .header-subtitle {
                font-size: 16px;
                margin: 5px 0;
              }
              .section {
                margin-bottom: 20px;
                border: 1px solid #000;
              }
              .section-header {
                background-color: #f0f0f0;
                padding: 8px;
                border-bottom: 1px solid #000;
              }
              .section-title {
                font-size: 16px;
                font-weight: bold;
                margin: 0;
              }
              .input-area {
                padding: 8px;
                min-height: 150px;
                white-space: pre-wrap;
              }
              .footer-fields {
                margin-top: 20px;
                display: flex;
                flex-direction: column;
                gap: 10px;
              }
              .field-row {
                display: flex;
                align-items: center;
                gap: 10px;
              }
              .field-label {
                font-weight: bold;
                min-width: 150px;
              }
              .field-value {
                border-bottom: 1px solid #000;
                padding: 2px 5px;
                flex: 1;
              }
            </style>
          </head>
          <body>
            <div class="page">
              <div class="header">
                <div class="header-top-row">
                  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." class="logo" alt="Hospital Logo"/>
                </div>
                <h1 class="header-title">CURA HOSPITALS</h1>
                <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
                <h2 class="header-subtitle">FORM(3)</h2>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">Procedure(s)</h3>
                </div>
                <div class="input-area">
                  ${formState.procedures || 'N/A'}
                </div>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">Additional Information</h3>
                </div>
                <div class="input-area">
                  ${formState.additionalInfo || 'N/A'}
                </div>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">Plan of Management</h3>
                </div>
                <div class="input-area">
                  ${formState.planOfManagement || 'N/A'}
                </div>
              </div>

              <div class="footer-fields">
                <div class="field-row">
                  <span class="field-label">As Ordered by Dr</span>
                  <span class="field-value">${formState.doctorName || 'N/A'}</span>
                </div>
                <div class="field-row">
                  <span class="field-label">Speciality</span>
                  <span class="field-value">${formState.speciality || 'N/A'}</span>
                </div>
              </div>
            </div>
          </body>
        </html>
      `;

      console.log('HTML content generated, creating PDF...');
      
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        width: 595, // A4 width in points
        height: 842, // A4 height in points
        base64: false
      });

      console.log('PDF generated at:', uri);

      // Pass the PDF data to the parent component
      if (onChange) {
        onChange({
          ...formState,
          pdfUri: uri,
          formType: 'diagnosisPage3'
        });
      }

      Alert.alert(
        'Success', 
        'PDF generated successfully!',
        [
          {
            text: 'View PDF',
            onPress: async () => {
              try {
                console.log('Opening PDF viewer...');
                const isAvailable = await Sharing.isAvailableAsync();
                if (isAvailable) {
                  await Sharing.shareAsync(uri, {
                    mimeType: 'application/pdf',
                    dialogTitle: 'View Diagnosis Form 3',
                    UTI: 'com.adobe.pdf'
                  });
                } else {
                  Alert.alert('Error', 'Sharing is not available on this device');
                }
              } catch (error) {
                console.error('Error opening PDF:', error);
                Alert.alert('Error', 'Failed to open PDF');
              }
            }
          },
          {
            text: 'OK',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert('Error', 'Failed to generate PDF');
    }
  };

  const handleReset = () => {
    // Reset all form fields to empty
    setFormState({
      procedures: '',
      additionalInfo: '',
      planOfManagement: '',
      doctorName: '',
      speciality: ''
    });
  };

  return (
    <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          {/* Top Row: Logos */}
          <View style={styles.headerTopRow}>
            <Image
              source={require('../assets/Logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          {/* Centered Text */}
          <View style={styles.headerCenteredText}>
            <Text style={styles.headerTitle}>CURA HOSPITALS</Text>
            <Text style={styles.headerSubtitle}>INPATIENT INITIAL ASSESSMENT</Text>
            <Text style={styles.headerSubtitle}>FORM(3)</Text>
          </View>
        </View>

        {/* Procedure(s) Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Procedure(s):</Text>
          </View>
          <TextInput
            style={[styles.input, styles.largeTextArea]}
            multiline
            numberOfLines={10}
            value={formState.procedures}
            onChangeText={(text) => setFormState(prev => ({ ...prev, procedures: text }))}
          />
        </View>

        {/* Additional Information Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Additional Information:</Text>
          </View>
          <TextInput
            style={[styles.input, styles.largeTextArea]}
            multiline
            numberOfLines={10}
            value={formState.additionalInfo}
            onChangeText={(text) => setFormState(prev => ({ ...prev, additionalInfo: text }))}
          />
        </View>

        {/* Plan of Management Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Plan of Management:</Text>
          </View>
          <TextInput
            style={[styles.input, styles.largeTextArea]}
            multiline
            numberOfLines={10}
            value={formState.planOfManagement}
            onChangeText={(text) => setFormState(prev => ({ ...prev, planOfManagement: text }))}
          />
        </View>

        {/* Doctor and Speciality */}
        <View style={styles.footerFields}>
          <View style={styles.fieldRow}>
            <Text style={styles.label}>As Ordered by Dr</Text>
            <TextInput
              style={[styles.input, styles.underlineInput, { flex: 1 }]}
              value={formState.doctorName}
              onChangeText={(text) => setFormState(prev => ({ ...prev, doctorName: text }))}
            />
          </View>
          <View style={styles.fieldRow}>
            <Text style={styles.label}>Speciality</Text>
            <TextInput
              style={[styles.input, styles.underlineInput, { flex: 1 }]}
              value={formState.speciality}
              onChangeText={(text) => setFormState(prev => ({ ...prev, speciality: text }))}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 10,
  },
  header: {
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#000',
    padding: 5,
  },
  headerTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  logo: {
    width: 350,
    height: 100,
  },
  headerCenteredText: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 16,
  },
  section: {
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#000',
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    padding: 8,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  largeTextArea: {
    height: 150,
  },
  footerFields: {
    marginTop: 20,
  },
  fieldRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  label: {
    fontSize: 16,
    marginRight: 5,
    fontWeight: '500',
  },
  underlineInput: {
    borderBottomWidth: 1,
    borderColor: '#000',
    borderRadius: 0,
    paddingHorizontal: 0,
    paddingVertical: 2,
    backgroundColor: 'transparent',
  },
});

export default DiagnosisPage3; 