import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { CameraView, Camera } from 'expo-camera';
import { supabase } from '../utils/supabase';

const QRScannerScreen = ({ navigation }) => {
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  const handleBarCodeScanned = async ({ data }) => {
    if (scanned) return;
    setScanned(true);
    setLoading(true);
    try {
      // Fetch patient details from users table using mrno
      const { data: patientData, error: patientError } = await supabase
        .from('users')
        .select('*')
        .eq('mrno', data)
        .maybeSingle();
      if (patientError || !patientData) {
        setLoading(false);
        Alert.alert('Not found', 'Patient not found');
        setScanned(false);
        return;
      }
      // Fetch recent appointments for this mrno, only required fields
      const { data: appointments, error: apptError } = await supabase
        .from('appointments')
        .select('patient_name,symptoms_brief,date,time')
        .eq('mrno', data)
        .order('date', { ascending: false })
        .limit(5);
      setLoading(false);
      if (navigation && navigation.navigate) {
        navigation.navigate('PatientDetails', { patient: patientData, appointments });
      } else {
        Alert.alert('Patient Details', `mrno: ${patientData.mrno}\nName: ${patientData.name || ''}`);
      }
    } catch (e) {
      setLoading(false);
      Alert.alert('Error', 'Error fetching patient details');
      setScanned(false);
    }
  };

  // Guard for Camera.Constants.BarCodeType.qr
  const qrType = Camera?.Constants?.BarCodeType?.qr || 'qr';

  if (hasPermission === null) {
    return <View style={styles.container}><ActivityIndicator size="large" color="#3498db" /></View>;
  }
  if (hasPermission === false) {
    return <View style={styles.container}><Text>No access to camera</Text></View>;
  }

  return (
    <View style={styles.container}>
      {loading && <ActivityIndicator size="large" color="#3498db" />}
      {!loading && (
        <CameraView
          style={StyleSheet.absoluteFillObject}
          onBarcodeScanned={handleBarCodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: ['qr'],
          }}
        />
      )}
      {!loading && scanned && (
        <View style={styles.overlay}>
          <Text style={styles.scanText}>Scanned! Processing...</Text>
        </View>
      )}
      {!loading && !scanned && (
        <View style={styles.overlay}>
          <Text style={styles.scanText}>Align QR code within frame</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  overlay: {
    position: 'absolute',
    top: 40,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  scanText: {
    color: '#fff',
    fontSize: 18,
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 8,
    borderRadius: 8,
  },
});

export default QRScannerScreen;

