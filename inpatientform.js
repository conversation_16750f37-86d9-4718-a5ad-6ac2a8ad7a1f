// InpatientForm.js

import React, { useState } from "react";

const InpatientForm = () => {
  const [date, setDate] = useState("");

  const handleDateChange = (e) => {
    const newDate = e.target.value;
    setDate(newDate);
    if (newDate.trim() !== "") {
      console.log("Date entered (non-empty):", newDate);
    }
  };

  return (
    <div style={{ fontFamily: "Arial, sans-serif", padding: "20px" }}>
      <div style={{ border: "1px solid black", marginBottom: "20px", padding: "10px" }}>
         <div style={{ fontWeight: "bold", marginBottom: "5px" }}>Date:</div>
         <input type="text" value={date} onChange={handleDateChange} placeholder="Enter date (e.g. MM/DD/YYYY)" style={{ width: "100%", padding: "5px" }} />
      </div>

      <div style={{ border: "1px solid black", display: "flex" }}>
        <div style={{ flex: 1, padding: "10px" }}>
          <img src="/logo.png" alt="Cura Hospital Logo" height="50" />
        </div>
        <div style={{ flex: 2, textAlign: "center", paddingTop: "20px" }}>
          <h2 style={{ margin: 0 }}>INPATIENT INITIAL ASSESSMENT FORM(1)</h2>
        </div>
        <div style={{ flex: 1 }} />
      </div>

      {/* Presenting Complaints */}
      <div style={{ border: "1px solid black", marginTop: "20px" }}>
        <div
          style={{
            borderBottom: "1px solid black",
            padding: "10px",
            backgroundColor: "#f0f0f0",
            fontWeight: "bold",
          }}
        >
          Presenting Complaints:
        </div>
        <textarea
          style={{ width: "100%", height: "100px", border: "none", padding: "10px" }}
          placeholder="Enter presenting complaints here..."
        />
      </div>

      {/* History of Presenting Complaints */}
      <div style={{ border: "1px solid black", marginTop: "20px" }}>
        <div
          style={{
            borderBottom: "1px solid black",
            padding: "10px",
            backgroundColor: "#f0f0f0",
            fontWeight: "bold",
          }}
        >
          History of Presenting Complaints:
        </div>
        <textarea
          style={{ width: "100%", height: "200px", border: "none", padding: "10px" }}
          placeholder="Enter history of presenting complaints here..."
        />
      </div>

      {/* Bottom Section */}
      <div style={{ display: "flex", marginTop: "20px" }}>
        {/* Past Medical / Surgical History */}
        <div style={{ flex: 1, border: "1px solid black", marginRight: "10px" }}>
          <div
            style={{
              borderBottom: "1px solid black",
              padding: "10px",
              backgroundColor: "#d0d0d0",
              fontWeight: "bold",
            }}
          >
            Past Medical / Surgical History
          </div>
          <textarea
            style={{ width: "100%", height: "200px", border: "none", padding: "10px" }}
            placeholder="Enter past medical or surgical history here..."
          />
        </div>

        {/* Regular Medication */}
        <div style={{ flex: 1, border: "1px solid black" }}>
          <div
            style={{
              borderBottom: "1px solid black",
              padding: "10px",
              backgroundColor: "#d0d0d0",
              fontWeight: "bold",
            }}
          >
            Regular Medication:
          </div>
          <textarea
            style={{ width: "100%", height: "200px", border: "none", padding: "10px" }}
            placeholder="Enter regular medications here..."
          />
        </div>
      </div>
    </div>
  );
};

export default InpatientForm;
