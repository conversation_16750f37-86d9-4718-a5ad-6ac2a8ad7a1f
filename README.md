# Hospital OPD App

<p>
  <!-- iOS -->
  <img alt="Supports Expo iOS" longdesc="Supports Expo iOS" src="https://img.shields.io/badge/iOS-4630EB.svg?style=flat-square&logo=APPLE&labelColor=999999&logoColor=fff" />
  <!-- Android -->
  <img alt="Supports Expo Android" longdesc="Supports Expo Android" src="https://img.shields.io/badge/Android-4630EB.svg?style=flat-square&logo=ANDROID&labelColor=A4C639&logoColor=fff" />
  <!-- Web -->
  <img alt="Supports Expo Web" longdesc="Supports Expo Web" src="https://img.shields.io/badge/web-4630EB.svg?style=flat-square&logo=GOOGLE-CHROME&labelColor=4285F4&logoColor=fff" />
</p>

## 📱 About

A simple React Native app for hospital OPD (Outpatient Department) management. This app allows doctors to log in with their credentials and access a dashboard with their information. Built with React Native, Expo SDK 53, and <PERSON><PERSON><PERSON> for database integration.

## 🔑 Features

- Doctor login with ID and password
- Simple dashboard showing doctor information
- Clean UI without tab-based navigation
- Supabase integration for database management

## 🚀 Getting Started

### Prerequisites

- Node.js
- npm or yarn
- Expo CLI

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Update the Supabase configuration in `src/utils/supabase.js` with your Supabase URL and anon key

### Database Setup

1. Create a Supabase account and project
2. Create a `doctors` table with the following columns:
   - `id` (auto-incrementing integer)
   - `doctor_id` (string, unique)
   - `password` (string)
   - `name` (string)
   - `specialization` (string)
3. Add sample doctor data for testing

### Running the App

```
npm start
```

Scan the QR code with the Expo Go app on your mobile device or use an emulator.

## 📝 Notes

- This app uses Expo SDK 53
- The app does not use tab-based navigation as requested
- Simple stack navigation is implemented for easy database integration
