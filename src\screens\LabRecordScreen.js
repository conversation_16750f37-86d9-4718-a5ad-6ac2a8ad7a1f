import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';

const LabRecordScreen = ({ route }) => {
  const { patient } = route.params || {};
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Lab Record</Text>
      </View>
      <View style={styles.content}>
        <Text style={styles.infoText}>This is a sample Lab Record screen.</Text>
        {patient && (
          <Text style={styles.patientText}>Patient: {patient.name} (MR No: {patient.mrno})</Text>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#8e44ad',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 18,
    color: '#2c3e50',
    marginBottom: 20,
  },
  patientText: {
    fontSize: 16,
    color: '#7f8c8d',
  },
});

export default LabRecordScreen; 