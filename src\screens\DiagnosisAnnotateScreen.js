import React, { useRef, useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, Alert, Image, Dimensions, Text, TouchableOpacity } from 'react-native';
import SignatureScreen from 'react-native-signature-canvas';
import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import { supabase } from '../utils/supabase';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const DiagnosisAnnotateScreen = ({ route, navigation }) => {
  const { mrno, templateImage } = route.params; // templateImage is a require() asset
  const [loading, setLoading] = useState(true);
  const [imageBg, setImageBg] = useState(null);
  const [assetUri, setAssetUri] = useState(null);
  const [imgDims, setImgDims] = useState({ width: screenWidth, height: screenHeight });
  const signatureRef = useRef();

  useEffect(() => {
    const loadImage = async () => {
      try {
        const resolved = Image.resolveAssetSource(templateImage);
        const uri = resolved.uri;
        setAssetUri(uri);
        Image.getSize(uri, (w, h) => {
          setImgDims({ width: w, height: h });
        }, () => {
          setImgDims({ width: screenWidth, height: screenHeight });
        });
        if (uri.startsWith('file://') || uri.startsWith('content://')) {
          const base64 = await FileSystem.readAsStringAsync(uri, { encoding: FileSystem.EncodingType.Base64 });
          setImageBg(`data:image/png;base64,${base64}`);
        } else {
          // Try fetch+FileReader for remote assets
          const response = await fetch(uri);
          const blob = await response.blob();
          const reader = new FileReader();
          reader.onloadend = () => {
            setImageBg(reader.result);
          };
          reader.readAsDataURL(blob);
        }
        setLoading(false);
      } catch (e) {
        setLoading(false);
        Alert.alert('Error', 'Failed to load template image');
      }
    };
    loadImage();
  }, [templateImage]);

  // When the doctor saves, return the annotation to the previous screen
  const handleOK = (signature) => {
    // signature is a base64 PNG image (data:image/png;base64,...)
    navigation.navigate({
      name: 'DiagnosisMultiAnnotateScreen',
      params: { newAnnotation: { image: signature.replace('data:image/png;base64,', ''), templateImage } },
      merge: true,
    });
  };

  // Calculate scaled dimensions to fit image in screen
  const aspectRatio = imgDims.width / imgDims.height;
  let canvasWidth = screenWidth;
  let canvasHeight = screenWidth / aspectRatio;
  if (canvasHeight > screenHeight) {
    canvasHeight = screenHeight;
    canvasWidth = screenHeight * aspectRatio;
  }

  return (
    <View style={styles.container}>
      {loading && <ActivityIndicator size="large" color="#3498db" />}
      {!loading && (
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', backgroundColor: '#fff' }}>
          {assetUri && (
            <Image source={{ uri: assetUri }} style={{ width: canvasWidth, height: canvasHeight, marginBottom: 8, borderWidth: 1, borderColor: '#eee' }} resizeMode="contain" />
          )}
          {imageBg || assetUri ? (
            <>
              <SignatureScreen
                ref={signatureRef}
                onOK={handleOK}
                descriptionText="Write diagnosis here"
                clearText="Clear"
                confirmText=""
                webStyle={webStyle}
                imageBackground={imageBg || assetUri}
                bgWidth={canvasWidth}
                bgHeight={canvasHeight}
                style={{ width: canvasWidth, height: canvasHeight }}
              />
              <TouchableOpacity
                style={styles.saveBtn}
                onPress={() => signatureRef.current.readSignature()}
              >
                <Text style={styles.saveBtnText}>Save</Text>
              </TouchableOpacity>
            </>
          ) : (
            <Text style={{ color: 'red' }}>Image not loaded</Text>
          )}
        </View>
      )}
    </View>
  );
};

const webStyle = `
  .m-signature-pad {
    box-shadow: none; border: none; margin: 0; padding: 0; width: 100vw; height: 100vh; max-width: 100vw; max-height: 100vh;
  }
  .m-signature-pad--body {
    border: none; margin: 0; padding: 0; width: 100vw; height: 100vh; max-width: 100vw; max-height: 100vh;
  }
  .m-signature-pad--footer {display: none; margin: 0px;}
  body,html {background: #fff; margin: 0; padding: 0; width: 100vw; height: 100vh; max-width: 100vw; max-height: 100vh;}
`;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  saveBtn: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    zIndex: 10,
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default DiagnosisAnnotateScreen;