import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Replace with your Supabase URL and anon key
const supabaseUrl = 'https://kdwkalgijuseaxbprdyh.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtkd2thbGdpanVzZWF4YnByZHloIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMjcyMTQsImV4cCI6MjA2MzYwMzIxNH0.assmST97KUpM9cUyqtzR4CDh6-250IT5hMwpNELdKMk';

// Create Supabase client with version 1.35.7 which has better React Native compatibility
// This version has a different API structure
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  localStorage: AsyncStorage,
  detectSessionInUrl: false,
  autoRefreshToken: true,
  persistSession: true,
});
