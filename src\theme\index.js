// Theme colors and styles - Lavender Theme
export const COLORS = {
  // Primary lavender shades
  primary: '#9678d3',
  primaryLight: '#e6e0f5',
  primaryDark: '#6a5acd',
  
  // Secondary colors
  secondary: '#a78bfa',
  secondaryLight: '#c4b5fd',
  
  // Accent colors
  accent: '#c084fc',
  accentLight: '#e9d5ff',
  
  // Neutrals
  background: '#faf9ff',
  card: '#ffffff',
  text: '#2d3748',
  textLight: '#718096',
  textLighter: '#a0aec0',
  border: '#e2e8f0',
  
  // Status colors
  success: '#48bb78',
  successLight: '#c6f6d5',
  warning: '#ecc94b',
  warningLight: '#fefcbf',
  error: '#f56565',
  errorLight: '#fed7d7',
  info: '#4299e1',
  infoLight: '#bee3f8',
  
  // Fixed colors
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
};

export const SHADOW = {
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 3,
};

export const CARD_STYLE = {
  backgroundColor: COLORS.card,
  borderRadius: 16,
  padding: 20,
  ...SHADOW,
};

export const INPUT_STYLE = {
  height: 56,
  backgroundColor: COLORS.white,
  borderRadius: 12,
  paddingHorizontal: 16,
  fontSize: 16,
  color: COLORS.text,
  borderWidth: 1,
  borderColor: COLORS.border,
  marginBottom: 16,
};

export const BUTTON_STYLE = {
  height: 56,
  borderRadius: 14,
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'row',
  ...SHADOW,
};

export const BUTTON_TEXT = {
  fontSize: 16,
  fontWeight: '600',
  color: COLORS.white,
};

export const TYPOGRAPHY = {
  h1: {
    fontSize: 28,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 8,
  },
  h2: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 8,
  },
  h3: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
  },
  body: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 24,
  },
  caption: {
    fontSize: 12,
    color: COLORS.textLight,
  },
};
