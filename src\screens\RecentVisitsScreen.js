import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { supabase } from '../utils/supabase';
import * as Linking from 'expo-linking';

const RecentVisitsScreen = () => {
  const [visits, setVisits] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRecentVisits();
  }, []);

  const fetchRecentVisits = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from('appointments')
      .select('appointment_id, date, time, patient_name, prescription_file_path')
      .not('prescription_file_path', 'is', null)
      .order('date', { ascending: false })
      .order('time', { ascending: false })
      .limit(10);
    if (error) {
      Alert.alert('Error', 'Failed to fetch recent visits');
      setVisits([]);
    } else {
      setVisits(data || []);
    }
    setLoading(false);
  };

  const openPDF = (pdfUrl) => {
    if (pdfUrl) {
      Linking.openURL(pdfUrl);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#fff', padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 16, textAlign: 'center' }}>Recent Visits</Text>
      {loading ? (
        <ActivityIndicator size="large" color="#007AFF" style={{ marginTop: 40 }} />
      ) : visits.length === 0 ? (
        <Text style={{ textAlign: 'center', color: '#888', marginTop: 40 }}>No recent visits with a prescription found.</Text>
      ) : (
        <FlatList
          data={visits}
          keyExtractor={item => item.appointment_id}
          renderItem={({ item }) => (
            <View
              style={{
                backgroundColor: '#f8f9fa',
                borderRadius: 8,
                padding: 16,
                marginBottom: 12,
                borderWidth: 1,
                borderColor: '#dee2e6',
              }}
            >
              <Text style={{ fontSize: 18, fontWeight: 'bold' }}>{item.patient_name}</Text>
              <Text style={{ color: '#555', marginTop: 4 }}>Date: {item.date}  Time: {item.time}</Text>
              {item.prescription_file_path ? (
                <TouchableOpacity
                  style={{ marginTop: 10, backgroundColor: '#007AFF', borderRadius: 6, padding: 10, alignItems: 'center' }}
                  onPress={() => openPDF(item.prescription_file_path)}
                >
                  <Text style={{ color: '#fff', fontWeight: 'bold' }}>View PDF</Text>
                </TouchableOpacity>
              ) : (
                <Text style={{ color: '#888', marginTop: 8 }}>No PDF available</Text>
              )}
            </View>
          )}
        />
      )}
    </View>
  );
};

export default RecentVisitsScreen; 