import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import React, { useEffect, useState } from 'react';
import { Alert, Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const DiagnosisPage1 = ({ formData, onChange, mrno }) => {
  const [formState, setFormState] = useState({
    presentingComplaints: '',
    historyOfComplaints: '',
    pastMedicalHistory: '',
    regularMedication: ''
  });

  // Load saved data when component mounts
  useEffect(() => {
    const loadSavedData = async () => {
      try {
        const keys = await AsyncStorage.getAllKeys();
        const savedKeys = keys.filter(key => key.startsWith(`diagnosisPage1Data_${mrno}_`));
        if (savedKeys.length > 0) {
          savedKeys.sort().reverse();
          const mostRecentKey = savedKeys[0];
          const savedData = await AsyncStorage.getItem(mostRecentKey);
          if (savedData) {
            setFormState(JSON.parse(savedData));
          }
        } else if (formData) {
          setFormState(formData);
        }
      } catch (error) {
        console.error('Error loading saved data:', error);
      }
    };
    loadSavedData();
  }, [formData, mrno]);

  // Update parent component whenever form state changes
  useEffect(() => {
    if (onChange) {
      onChange(formState);
    }
  }, [formState]);

  const handleSave = async () => {
    try {
      const timestamp = new Date().toISOString();
      const key = `diagnosisPage1Data_${mrno}_${timestamp}`;
      await AsyncStorage.setItem(key, JSON.stringify(formState));
      Alert.alert('Success', 'Diagnosis Page 1 data saved permanently!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save data');
    }
  };

  const handleSaveForm = async () => {
    try {
      console.log('Starting PDF generation...');
      
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              @page {
                size: A4;
                margin: 0;
              }
              body { 
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #000;
              }
              .page { 
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #000;
                background: white;
                min-height: 800px;
              }
              .header { 
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
              }
              .header-top-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
              }
              .logo {
                width: 350px;
                height: 100px;
                object-fit: contain;
              }
              .header-title {
                font-size: 20px;
                font-weight: bold;
                margin: 0;
              }
              .header-subtitle {
                font-size: 16px;
                margin: 5px 0;
              }
              .section {
                margin-bottom: 20px;
                border: 1px solid #000;
              }
              .section-header {
                background-color: #f0f0f0;
                padding: 8px;
                border-bottom: 1px solid #000;
              }
              .section-title {
                font-size: 16px;
                font-weight: bold;
                margin: 0;
              }
              .input-area {
                padding: 8px;
                min-height: 150px;
                white-space: pre-wrap;
              }
              .two-column-section {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
              }
              .column {
                flex: 1;
                border: 1px solid #000;
                margin-right: 10px;
              }
              .column:last-child {
                margin-right: 0;
              }
            </style>
          </head>
          <body>
            <div class="page">
              <div class="header">
                <div class="header-top-row">
                  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." class="logo" alt="Hospital Logo"/>
                </div>
                <h1 class="header-title">CURA HOSPITALS</h1>
                <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
                <h2 class="header-subtitle">FORM(1)</h2>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">Presenting Complaints</h3>
                </div>
                <div class="input-area">
                  ${formState.presentingComplaints || 'N/A'}
                </div>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">History of Presenting Complaints</h3>
                </div>
                <div class="input-area">
                  ${formState.historyOfComplaints || 'N/A'}
                </div>
              </div>

              <div class="two-column-section">
                <div class="column">
                  <div class="section-header">
                    <h3 class="section-title">Past Medical / Surgical History</h3>
                  </div>
                  <div class="input-area">
                    ${formState.pastMedicalHistory || 'N/A'}
                  </div>
                </div>
                <div class="column">
                  <div class="section-header">
                    <h3 class="section-title">Regular Medication</h3>
                  </div>
                  <div class="input-area">
                    ${formState.regularMedication || 'N/A'}
                  </div>
                </div>
              </div>
            </div>
          </body>
        </html>
      `;

      console.log('HTML content generated, creating PDF...');
      
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        width: 595, // A4 width in points
        height: 842, // A4 height in points
        base64: false
      });

      console.log('PDF generated at:', uri);

      // Pass the PDF data to the parent component
      if (onChange) {
        onChange({
          ...formState,
          pdfUri: uri,
          formType: 'diagnosisPage1',
          content: htmlContent
        });
      }

      Alert.alert(
        'Success', 
        'PDF generated successfully!',
        [
          {
            text: 'View PDF',
            onPress: async () => {
              try {
                console.log('Opening PDF viewer...');
                const isAvailable = await Sharing.isAvailableAsync();
                if (isAvailable) {
                  await Sharing.shareAsync(uri, {
                    mimeType: 'application/pdf',
                    dialogTitle: 'View Diagnosis Form 1',
                    UTI: 'com.adobe.pdf'
                  });
                } else {
                  Alert.alert('Error', 'Sharing is not available on this device');
                }
              } catch (error) {
                console.error('Error opening PDF:', error);
                Alert.alert('Error', 'Failed to open PDF');
              }
            }
          },
          {
            text: 'OK',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert('Error', 'Failed to generate PDF');
    }
  };

  const handleReset = () => {
    // Reset all form fields to empty
    setFormState({
      presentingComplaints: '',
      historyOfComplaints: '',
      pastMedicalHistory: '',
      regularMedication: ''
    });
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        {/* Top Row: Logos */}
        <View style={styles.headerTopRow}>
          <Image
            source={require('../assets/Logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* Centered Text */}
        <View style={styles.headerCenteredText}>
          <Text style={styles.headerTitle}>CURA HOSPITALS</Text>
          <Text style={styles.headerSubtitle}>INPATIENT INITIAL ASSESSMENT</Text>
          <Text style={styles.headerSubtitle}>FORM(1)</Text>
        </View>
      </View>

      {/* Presenting Complaints Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Presenting Complaints</Text>
        </View>
        <View style={styles.sectionContent}>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={12}
            placeholder="Enter presenting complaints..."
            value={formState.presentingComplaints}
            onChangeText={(text) => setFormState(prev => ({ ...prev, presentingComplaints: text }))}
          />
        </View>
      </View>

      {/* History of Presenting Complaints Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>History of Presenting Complaints:</Text>
        </View>
        <View style={styles.sectionContent}>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={12}
            placeholder="Enter history of presenting complaints..."
            value={formState.historyOfComplaints}
            onChangeText={(text) => setFormState(prev => ({ ...prev, historyOfComplaints: text }))}
          />
        </View>
      </View>

      {/* Past Medical / Surgical History & Regular Medication Section */}
      <View style={[styles.section, styles.twoColumnSection]}>
        <View style={styles.column}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Past Medical / Surgical History</Text>
          </View>
          <View style={styles.sectionContent}>
            <TextInput
              style={[styles.input, styles.textArea]}
              multiline
              numberOfLines={12}
              placeholder="Enter past medical/surgical history..."
              value={formState.pastMedicalHistory}
              onChangeText={(text) => setFormState(prev => ({ ...prev, pastMedicalHistory: text }))}
            />
          </View>
        </View>
        <View style={styles.column}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Regular Medication:</Text>
          </View>
          <View style={styles.sectionContent}>
            <TextInput
              style={[styles.input, styles.textArea]}
              multiline
              numberOfLines={12}
              placeholder="Enter regular medications..."
              value={formState.regularMedication}
              onChangeText={(text) => setFormState(prev => ({ ...prev, regularMedication: text }))}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 10,
  },
  header: {
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#000',
    padding: 5,
  },
  headerTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  logo: {
    width: 350,
    height: 100,
  },
  headerCenteredText: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 16,
  },
  section: {
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 0,
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionContent: {
    padding: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 16,
  },
  textArea: {
    minHeight: 180,
    textAlignVertical: 'top',
  },
  twoColumnSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    borderWidth: 0,
  },
  column: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#000',
    marginHorizontal: 0,
    marginRight: 10,
  },
});

export default DiagnosisPage1;