import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ImageBackground,
  Dimensions,
  Animated,
  Easing
} from 'react-native';
import { supabase } from '../utils/supabase';
import { MaterialIcons, FontAwesome5, Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

// Import theme colors
import { COLORS } from '../theme';

const DashboardScreen = ({ route, navigation }) => {
  // Animation values
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(30))[0];

  // Animate on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      })
    ]).start();
  }, []);
  // Get the doctor data passed from the login screen
  const { doctor } = route.params || { doctor: { name: 'Doctor', specialization: 'General' } };

  // State for appointment stats
  const [stats, setStats] = useState({ today: 0, pending: 0, completed: 0 });
  const [loadingStats, setLoadingStats] = useState(false);
  const [hasNewAppointment, setHasNewAppointment] = useState(false);
  const [todayPatients, setTodayPatients] = useState([]);

  useEffect(() => {
    const fetchStats = async () => {
      setLoadingStats(true);
      try {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const todayStr = `${yyyy}-${mm}-${dd}`;
        // Fetch all appointments for this doctor from the correct table
        const { data: appointments, error } = await supabase
          .from('appointments')
          .select('*')
          .eq('doctor_id', doctor.doctor_id);
        if (error) throw error;
        // Calculate stats using correct fields
        const todayPatientsList = appointments.filter(a => a.date === todayStr);
        setTodayPatients(todayPatientsList);
        const todayCount = todayPatientsList.length;
        const pendingCount = appointments.filter(a => a.status === 'pending').length;
        const completedCount = appointments.filter(a => a.status === 'completed').length;
        setStats({ today: todayCount, pending: pendingCount, completed: completedCount });
        setHasNewAppointment(pendingCount > 0);
      } catch (e) {
        setStats({ today: 0, pending: 0, completed: 0 });
        setHasNewAppointment(false);
        setTodayPatients([]);
      } finally {
        setLoadingStats(false);
      }
    };
    fetchStats();
    // Real-time subscription for new appointments
    const subscription = supabase
      .from(`appointments:doctor_id=eq.${doctor.doctor_id}`)
      .on('INSERT', payload => {
        setHasNewAppointment(true);
        fetchStats();
      })
      .subscribe();
    return () => {
      supabase.removeSubscription(subscription);
    };
  }, [doctor]);

  const handleLogout = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Login' }],
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <Animated.View 
        style={[
          styles.header,
          { 
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }] 
          }
        ]}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerSubtitle}>Welcome to</Text>
            <Text style={styles.headerTitle}>MediCare OPD</Text>
          </View>
          <TouchableOpacity 
            style={styles.logoutButton} 
            onPress={handleLogout}
            activeOpacity={0.8}
          >
            <Ionicons name="log-out-outline" size={20} color="#fff" />
            <Text style={styles.logoutButtonText}> Logout</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      <Animated.View 
        style={[
          styles.doctorInfoCard,
          { 
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }] 
          }
        ]}
      >
        <View style={styles.doctorHeader}>
          <View style={styles.avatar}>
            <FontAwesome5 name="user-md" size={24} color={COLORS.primary} />
          </View>
          <View style={styles.doctorHeaderContent}>
            <Text style={styles.welcomeText}>Welcome back,</Text>
            <Text style={styles.doctorName}>{doctor.name}</Text>
            <View style={styles.specializationBadge}>
              <Text style={styles.doctorSpecialization}>{doctor.specialization}</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.additionalInfoContainer}>
          <View style={styles.infoItem}>
            <View style={styles.infoIcon}>
              <MaterialIcons name="medical-services" size={18} color={COLORS.primary} />
            </View>
            <View>
              <Text style={styles.infoLabel}>Department</Text>
              <Text style={styles.infoValue}>{doctor.department || 'Not specified'}</Text>
            </View>
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.infoItem}>
            <View style={styles.infoIcon}>
              <MaterialIcons name="school" size={18} color={COLORS.primary} />
            </View>
            <View>
              <Text style={styles.infoLabel}>Qualification</Text>
              <Text style={styles.infoValue}>{doctor.qualification || 'Not specified'}</Text>
            </View>
          </View>
        </View>
      </Animated.View>

      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Today's Overview</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.statsContainer}
          >
          <Animated.View 
            style={[
              styles.statCard,
              { backgroundColor: '#e6f2ff' },
              { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }
            ]}
          >
            <View style={styles.statIcon}>
              <FontAwesome5 name="user-injured" size={20} color={COLORS.primary} />
            </View>
            <Text style={[styles.statNumber, { color: COLORS.primary }]}>
              {loadingStats ? '-' : stats.today}
            </Text>
            <Text style={styles.statLabel}>Today's Patients</Text>
          </Animated.View>
          
          <Animated.View 
            style={[
              styles.statCard,
              { backgroundColor: '#fff5e6' },
              { 
                opacity: fadeAnim,
                transform: [
                  { translateY: slideAnim },
                  { 
                    translateX: slideAnim.interpolate({
                      inputRange: [0, 30],
                      outputRange: [0, 30]
                    })
                  }
                ] 
              }
            ]}
          >
            <View style={[styles.statIcon, { backgroundColor: 'rgba(251, 191, 36, 0.1)' }]}>
              <MaterialIcons name="pending-actions" size={20} color={COLORS.warning} />
            </View>
            <Text style={[styles.statNumber, { color: COLORS.warning }]}>
              {loadingStats ? '-' : stats.pending}
            </Text>
            <Text style={styles.statLabel}>Pending</Text>
          </Animated.View>
          
          <Animated.View 
            style={[
              styles.statCard,
              { backgroundColor: '#e6f7f0' },
              { 
                opacity: fadeAnim,
                transform: [
                  { translateY: slideAnim },
                  { 
                    translateX: slideAnim.interpolate({
                      inputRange: [0, 30],
                      outputRange: [0, 60]
                    })
                  }
                ] 
              }
            ]}
          >
            <View style={[styles.statIcon, { backgroundColor: 'rgba(16, 185, 129, 0.1)' }]}>
              <MaterialIcons name="check-circle" size={20} color={COLORS.success} />
            </View>
            <Text style={[styles.statNumber, { color: COLORS.success }]}>
              {loadingStats ? '-' : stats.completed}
            </Text>
            <Text style={styles.statLabel}>Completed</Text>
          </Animated.View>
          </ScrollView>
        </View>

        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <Animated.View 
            style={[
              styles.actionsContainer,
              { 
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }] 
              }
            ]}
          >
            <View style={styles.centeredButtonContainer}>
              <TouchableOpacity 
                style={[styles.actionButton, styles.primaryActionButton]}
                onPress={() => navigation.navigate('QRScanner')}
                activeOpacity={0.9}
              >
                <View style={styles.actionIconContainer}>
                  <MaterialIcons name="qr-code-scanner" size={32} color="#fff" />
                </View>
                <View style={styles.buttonTextContainer}>
                  <Text style={[styles.actionButtonText, styles.primaryActionText]}>Scan Patient QR</Text>
                  <Text style={styles.actionDescription}>Quickly access patient records</Text>
                </View>
              </TouchableOpacity>
            </View>
            
            <View style={styles.actionRow}>
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryActionButton]}
                onPress={() => navigation.navigate('PatientSearch')}
                activeOpacity={0.9}
              >
                <MaterialIcons name="search" size={20} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, { marginLeft: 8 }]}>
                  Search Patient
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryActionButton]}
                onPress={() => { 
                  setHasNewAppointment(false); 
                  navigation.navigate('Appointments', { doctor, todayPatients }); 
                }}
                activeOpacity={0.9}
              >
                <View style={{ position: 'relative' }}>
                  <MaterialIcons name="event-note" size={20} color={COLORS.primary} />
                  {hasNewAppointment && (
                    <View style={styles.notificationBadge}>
                      <Text style={styles.notificationText}>{stats.pending > 9 ? '9+' : stats.pending}</Text>
                    </View>
                  )}
                </View>
                <Text style={[styles.actionButtonText, { marginLeft: 8 }]}>
                  View Schedule
                </Text>
              </TouchableOpacity>
            </View>
            

          </Animated.View>
        </View>
        
        <View style={styles.upcomingContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming Appointments</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {todayPatients.length > 0 ? (
            <View style={styles.appointmentList}>
              {todayPatients.slice(0, 3).map((patient, index) => (
                <View key={index} style={styles.appointmentCard}>
                  <View style={styles.appointmentTime}>
                    <Text style={styles.appointmentHour}>09:00</Text>
                    <Text style={styles.appointmentAmPm}>AM</Text>
                  </View>
                  <View style={styles.appointmentDetails}>
                    <Text style={styles.appointmentName}>
                      {patient.patient_name || 'Patient Name'}
                    </Text>
                    <Text style={styles.appointmentReason}>
                      {patient.reason || 'Routine Checkup'}
                    </Text>
                  </View>
                  <View style={styles.appointmentActions}>
                    <TouchableOpacity style={styles.appointmentActionButton}>
                      <MaterialIcons name="phone" size={16} color={COLORS.primary} />
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={[
                        styles.appointmentActionButton,
                        { backgroundColor: COLORS.primary }
                      ]}
                    >
                      <MaterialIcons name="chat" size={16} color="#fff" />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.noAppointments}>
              <MaterialIcons name="event-available" size={40} color={COLORS.border} />
              <Text style={styles.noAppointmentsText}>No appointments scheduled for today</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    paddingBottom: 0,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingTop: 30,
    paddingBottom: 12,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    elevation: 4,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    marginBottom: 0,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 2,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: '#fff',
    marginBottom: 4,
    letterSpacing: 0.5,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  logoutButtonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14,
  },
  doctorInfoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginHorizontal: 12,
    marginTop: -6,
    marginBottom: 4,
    borderRadius: 14,
    padding: 14,
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.12)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
    zIndex: 1,
  },
  doctorHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  patientImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    borderWidth: 1,
    borderColor: COLORS.primary + '40',
  },
  doctorHeaderContent: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textLight,
    marginBottom: 4,
  },
  welcomeSubtext: {
    fontSize: 14,
    color: COLORS.textLight,
  },
  specializationBadge: {
    backgroundColor: 'rgba(26, 115, 232, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  doctorSpecialization: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.primary,
  },
  additionalInfoContainer: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(150, 120, 211, 0.1)',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 18,
  },
  infoIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: 'rgba(150, 120, 211, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.primary + '40',
  },
  infoLabel: {
    fontSize: 12,
    color: COLORS.textLight,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: 8,
  },
  content: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -12,
    paddingTop: 8,
  },
  scrollContent: {
    paddingBottom: 16,
    paddingTop: 6,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 12,
    paddingVertical: 2,
    paddingRight: 12,
    width: '100%',
  },
  statCard: {
    width: (Dimensions.get('window').width - 72) / 3, // Calculate width based on screen width
    marginRight: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 10,
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.1)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 1,
    justifyContent: 'space-between',
    minHeight: 110,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: COLORS.textLight,
    marginBottom: 8,
  },
  sectionContainer: {
    marginHorizontal: 12,
    marginBottom: 8,
    width: '100%',
  },
  doctorName: {
    fontSize: 22,
    fontWeight: '700',
    color: COLORS.primaryDark,
    marginBottom: 8,
    lineHeight: 28,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: COLORS.primaryDark,
    marginBottom: 8,
    marginLeft: 10,
    letterSpacing: 0.1,
  },
  actionsContainer: {
    marginBottom: 8,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  actionButton: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 18,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.15)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 12,
    elevation: 3,
  },
  centeredButtonContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryActionButton: {
    backgroundColor: COLORS.primaryDark,
    paddingVertical: 20,
    paddingHorizontal: 28,
    borderRadius: 16,
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '90%',
    maxWidth: 400,
  },
  buttonTextContainer: {
    marginLeft: 16,
  },
  secondaryActionButton: {
    backgroundColor: '#fff',
    flex: 1,
    marginRight: 8,
    justifyContent: 'center',
    paddingVertical: 14,
  },
  tertiaryActionButton: {
    backgroundColor: '#fff',
    flex: 1,
    marginRight: 8,
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  actionIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  upcomingContainer: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  seeAllText: {
    color: COLORS.primary,
    fontWeight: '500',
    fontSize: 14,
  },
  appointmentList: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.1)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 12,
    elevation: 3,
  },
  appointmentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  appointmentTime: {
    alignItems: 'center',
    marginRight: 16,
    minWidth: 60,
  },
  appointmentHour: {
    fontSize: 16,
    fontWeight: '700',
    color: COLORS.text,
  },
  appointmentAmPm: {
    fontSize: 12,
    color: COLORS.textLight,
    marginTop: 2,
  },
  appointmentDetails: {
    flex: 1,
  },
  appointmentName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  appointmentReason: {
    fontSize: 13,
    color: COLORS.textLight,
  },
  appointmentActions: {
    flexDirection: 'row',
  },
  appointmentActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(26, 115, 232, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(150, 120, 211, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 18,
    borderWidth: 2,
    borderColor: 'rgba(150, 120, 211, 0.2)',
  },
  noAppointments: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(150, 120, 211, 0.1)',
    shadowColor: COLORS.primaryDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 2,
  },
  noAppointmentsText: {
    marginTop: 12,
    fontSize: 14,
    color: COLORS.textLight,
    textAlign: 'center',
  },
  // Add any additional styles here
});

export default DashboardScreen;
