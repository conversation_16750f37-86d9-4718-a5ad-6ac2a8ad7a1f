import * as FileSystem from 'expo-file-system';
import React, { useState } from 'react';
import { ActivityIndicator, Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { PDFDocument, PDFPage } from 'react-native-pdf-lib';
import { supabase } from '../utils/supabase';

const DiagnosisMultiAnnotateScreen = ({ route, navigation }) => {
  const { mrno, userName } = route.params;
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(null);

  // Called when returning from form
  React.useEffect(() => {
    if (route.params?.newAnnotation?.formData) {
      setFormData(route.params.newAnnotation.formData);
      navigation.setParams({ newAnnotation: undefined });
    }
  }, [route.params?.newAnnotation]);

  const handleSave = async () => {
    if (!formData) {
      Alert.alert('No diagnosis', 'Please complete the diagnosis form.');
      return;
    }
    setLoading(true);
    try {
      // Generate a PDF with the form data
      const now = new Date();
      const dateStr = now.toLocaleDateString();
      const timeStr = now.toLocaleTimeString();
      
      const pdfPath = `${FileSystem.cacheDirectory}${now.toISOString().replace(/[:.]/g, '-')}_${mrno}_diagnosis.pdf`;
      
      // Create PDF with form data
      const pages = [];
      // Add form data to PDF pages
      const page = PDFPage
        .create()
        .setMediaBox(612, 792)
        .drawText(`Diagnosis Form for Patient: ${mrno}`, {
          x: 20,
          y: 750,
          color: '#333333',
          fontSize: 16,
        })
        .drawText(`Date: ${dateStr}  Time: ${timeStr}  User: ${userName || ''}`, {
          x: 20,
          y: 720,
          color: '#333333',
          fontSize: 14,
        })
        .drawText(JSON.stringify(formData, null, 2), {
          x: 20,
          y: 680,
          color: '#333333',
          fontSize: 12,
        });
      pages.push(page);

      await PDFDocument.create(pdfPath).addPages(...pages).write();

      // Upload PDF to Supabase Storage
      const filename = pdfPath.split('/').pop();
      const { data, error } = await supabase.storage
        .from('diagnosis-pdfs')
        .upload(filename, { uri: pdfPath, type: 'application/pdf' }, { upsert: true, contentType: 'application/pdf' });
      
      if (error) throw error;

      // Get public URL
      const { data: publicUrlData } = supabase.storage.from('diagnosis-pdfs').getPublicUrl(filename);
      const publicUrl = publicUrlData.publicUrl;

      // Save the link in the user's diagnosis column
      const { error: updateError } = await supabase
        .from('users')
        .update({ diagnosis: publicUrl })
        .eq('mrno', mrno);

      if (updateError) throw updateError;

      setLoading(false);
      setFormData(null); // Clear form data only on success
      Alert.alert('Success', 'Diagnosis form saved and uploaded!');
      navigation.goBack();
    } catch (e) {
      setLoading(false);
      Alert.alert('Error', e.message || 'Failed to save diagnosis form');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Diagnosis Form</Text>
      
      {!formData ? (
        <TouchableOpacity 
          style={styles.createButton}
          onPress={() => navigation.navigate('DiagnosisFormScreen', { mrno })}
        >
          <Text style={styles.createButtonText}>Create New Diagnosis</Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.formPreview}>
          <Text style={styles.previewTitle}>Form Preview</Text>
          <Text style={styles.previewText}>Form data has been captured. Click Save to store the diagnosis.</Text>
          <TouchableOpacity 
            style={styles.editButton}
            onPress={() => navigation.navigate('DiagnosisFormScreen', { mrno })}
          >
            <Text style={styles.editButtonText}>Edit Form</Text>
          </TouchableOpacity>
        </View>
      )}

      <TouchableOpacity 
        style={[styles.saveBtn, !formData && styles.saveBtnDisabled]} 
        onPress={handleSave} 
        disabled={loading || !formData}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.saveBtnText}>Save Diagnosis</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  createButton: {
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 20,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  formPreview: {
    backgroundColor: '#f8f9fa',
    padding: 20,
    borderRadius: 8,
    marginVertical: 20,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  previewText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  editButton: {
    backgroundColor: '#2ecc71',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  editButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  saveBtn: {
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 'auto',
  },
  saveBtnDisabled: {
    backgroundColor: '#bdc3c7',
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default DiagnosisMultiAnnotateScreen;