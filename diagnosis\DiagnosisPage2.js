import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import React, { useEffect, useState } from 'react';
import { Alert, Image, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

const DiagnosisPage2 = ({ formData, onChange, mrno }) => {
  const [formState, setFormState] = useState({
    familyHistory: '',
    allergies: '',
    pallorChecked: false,
    icterusChecked: false,
    cyanosisChecked: false,
    clubbingChecked: false,
    lymphadenopathyChecked: false,
    pedalEdemaChecked: false,
    others: '',
    hr: '',
    bp: '',
    rr: '',
    spo2: '',
    temp: '',
    airway: '',
    rs: '',
    cvs: '',
    pa: '',
    cns: '',
    pain: '',
    localExamination: '',
    investigationDone: ''
  });

  // Load saved data when component mounts
  useEffect(() => {
    const loadSavedData = async () => {
      try {
        const keys = await AsyncStorage.getAllKeys();
        const savedKeys = keys.filter(key => key.startsWith(`diagnosisPage2Data_${mrno}_`));
        if (savedKeys.length > 0) {
          savedKeys.sort().reverse();
          const mostRecentKey = savedKeys[0];
          const savedData = await AsyncStorage.getItem(mostRecentKey);
          if (savedData) {
            setFormState(JSON.parse(savedData));
          }
        } else if (formData) {
          setFormState(formData);
        }
      } catch (error) {
        console.error('Error loading saved data:', error);
      }
    };
    loadSavedData();
  }, [formData, mrno]);

  // Update parent component whenever form state changes
  useEffect(() => {
    if (onChange) {
      onChange(formState);
    }
  }, [formState]);

  const handleSave = async () => {
    try {
      const timestamp = new Date().toISOString();
      const key = `diagnosisPage2Data_${mrno}_${timestamp}`;
      await AsyncStorage.setItem(key, JSON.stringify(formState));
      Alert.alert('Success', 'Diagnosis Page 2 data saved permanently!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save data');
    }
  };

  const handleSaveForm = async () => {
    try {
      console.log('Starting PDF generation...');
      
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              @page {
                size: A4;
                margin: 0;
              }
              body { 
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #000;
              }
              .page { 
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #000;
                background: white;
                min-height: 800px;
              }
              .header { 
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
              }
              .header-top-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
              }
              .logo {
                width: 350px;
                height: 100px;
                object-fit: contain;
              }
              .header-title {
                font-size: 20px;
                font-weight: bold;
                margin: 0;
              }
              .header-subtitle {
                font-size: 16px;
                margin: 5px 0;
              }
              .section {
                margin-bottom: 20px;
                border: 1px solid #000;
              }
              .section-header {
                background-color: #f0f0f0;
                padding: 8px;
                border-bottom: 1px solid #000;
              }
              .section-title {
                font-size: 16px;
                font-weight: bold;
                margin: 0;
              }
              .input-area {
                padding: 8px;
                min-height: 150px;
                white-space: pre-wrap;
              }
              .two-column-section {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
              }
              .column {
                flex: 1;
                border: 1px solid #000;
                margin-right: 10px;
              }
              .column:last-child {
                margin-right: 0;
              }
              .checkbox-list {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                padding: 8px;
              }
              .checkbox-item {
                display: flex;
                align-items: center;
                margin-right: 15px;
              }
              .checkbox {
                margin-right: 5px;
              }
              .vitals-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
                padding: 8px;
              }
              .vital-item {
                display: flex;
                align-items: center;
              }
              .vital-label {
                font-weight: bold;
                margin-right: 5px;
              }
              .vital-value {
                border-bottom: 1px solid #000;
                padding: 2px 5px;
                min-width: 50px;
                text-align: center;
              }
              .vital-unit {
                margin-left: 5px;
                font-size: 12px;
              }
            </style>
          </head>
          <body>
            <div class="page">
              <div class="header">
                <div class="header-top-row">
                  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." class="logo" alt="Hospital Logo"/>
                </div>
                <h1 class="header-title">CURA HOSPITALS</h1>
                <h2 class="header-subtitle">INPATIENT INITIAL ASSESSMENT</h2>
                <h2 class="header-subtitle">FORM(2)</h2>
              </div>

              <div class="two-column-section">
                <div class="column">
                  <div class="section-header">
                    <h3 class="section-title">Family/Social/Occupational History</h3>
                  </div>
                  <div class="input-area">
                    ${formState.familyHistory || 'N/A'}
                  </div>
                </div>
                <div class="column">
                  <div class="section-header">
                    <h3 class="section-title">Allergies</h3>
                  </div>
                  <div class="input-area">
                    ${formState.allergies || 'N/A'}
                  </div>
                </div>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">General Physical Examination</h3>
                </div>
                <div class="checkbox-list">
                  <div class="checkbox-item">
                    <span class="checkbox">${formState.pallorChecked ? '☑' : '□'}</span>
                    <span>Pallor</span>
                  </div>
                  <div class="checkbox-item">
                    <span class="checkbox">${formState.icterusChecked ? '☑' : '□'}</span>
                    <span>Icterus</span>
                  </div>
                  <div class="checkbox-item">
                    <span class="checkbox">${formState.cyanosisChecked ? '☑' : '□'}</span>
                    <span>Cyanosis</span>
                  </div>
                  <div class="checkbox-item">
                    <span class="checkbox">${formState.clubbingChecked ? '☑' : '□'}</span>
                    <span>Clubbing</span>
                  </div>
                  <div class="checkbox-item">
                    <span class="checkbox">${formState.lymphadenopathyChecked ? '☑' : '□'}</span>
                    <span>Lymphadenopathy</span>
                  </div>
                  <div class="checkbox-item">
                    <span class="checkbox">${formState.pedalEdemaChecked ? '☑' : '□'}</span>
                    <span>Pedal Edema</span>
                  </div>
                </div>
                <div class="input-area">
                  <strong>Others:</strong> ${formState.others || 'N/A'}
                </div>
              </div>

              <div class="section">
                <div class="section-header">
                  <h3 class="section-title">Vitals</h3>
                </div>
                <div class="vitals-grid">
                  <div class="vital-item">
                    <span class="vital-label">HR:</span>
                    <span class="vital-value">${formState.hr || 'N/A'}</span>
                    <span class="vital-unit">/min</span>
                  </div>
                  <div class="vital-item">
                    <span class="vital-label">BP:</span>
                    <span class="vital-value">${formState.bp || 'N/A'}</span>
                    <span class="vital-unit">mmHg</span>
                  </div>
                  <div class="vital-item">
                    <span class="vital-label">RR:</span>
                    <span class="vital-value">${formState.rr || 'N/A'}</span>
                    <span class="vital-unit">/min</span>
                  </div>
                  <div class="vital-item">
                    <span class="vital-label">SpO2:</span>
                    <span class="vital-value">${formState.spo2 || 'N/A'}</span>
                    <span class="vital-unit">%</span>
                  </div>
                  <div class="vital-item">
                    <span class="vital-label">Temp:</span>
                    <span class="vital-value">${formState.temp || 'N/A'}</span>
                    <span class="vital-unit">°F</span>
                  </div>
                </div>
              </div>

              <div class="two-column-section">
                <div class="column">
                  <div class="section-header">
                    <h3 class="section-title">Systemic Examination</h3>
                  </div>
                  <div class="input-area">
                    <p><strong>Airway:</strong> ${formState.airway || 'N/A'}</p>
                    <p><strong>RS:</strong> ${formState.rs || 'N/A'}</p>
                    <p><strong>CVS:</strong> ${formState.cvs || 'N/A'}</p>
                    <p><strong>P/A:</strong> ${formState.pa || 'N/A'}</p>
                    <p><strong>CNS:</strong> ${formState.cns || 'N/A'}</p>
                    <p><strong>Pain:</strong> ${formState.pain || 'N/A'}</p>
                    <p><strong>Local Examination:</strong> ${formState.localExamination || 'N/A'}</p>
                  </div>
                </div>
                <div class="column">
                  <div class="section-header">
                    <h3 class="section-title">Investigation Done</h3>
                  </div>
                  <div class="input-area">
                    ${formState.investigationDone || 'N/A'}
                  </div>
                </div>
              </div>
            </div>
          </body>
        </html>
      `;

      console.log('HTML content generated, creating PDF...');
      
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        width: 595, // A4 width in points
        height: 842, // A4 height in points
        base64: false
      });

      console.log('PDF generated at:', uri);

      // Pass the PDF data to the parent component
      if (onChange) {
        onChange({
          ...formState,
          pdfUri: uri,
          formType: 'diagnosisPage2'
        });
      }

      Alert.alert(
        'Success', 
        'PDF generated successfully!',
        [
          {
            text: 'View PDF',
            onPress: async () => {
              try {
                console.log('Opening PDF viewer...');
                const isAvailable = await Sharing.isAvailableAsync();
                if (isAvailable) {
                  await Sharing.shareAsync(uri, {
                    mimeType: 'application/pdf',
                    dialogTitle: 'View Diagnosis Form 2',
                    UTI: 'com.adobe.pdf'
                  });
                } else {
                  Alert.alert('Error', 'Sharing is not available on this device');
                }
              } catch (error) {
                console.error('Error opening PDF:', error);
                Alert.alert('Error', 'Failed to open PDF');
              }
            }
          },
          {
            text: 'OK',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert('Error', 'Failed to generate PDF');
    }
  };

  const handleReset = () => {
    // Reset all form fields to empty
    setFormState({
      familyHistory: '',
      allergies: '',
      pallorChecked: false,
      icterusChecked: false,
      cyanosisChecked: false,
      clubbingChecked: false,
      lymphadenopathyChecked: false,
      pedalEdemaChecked: false,
      others: '',
      hr: '',
      bp: '',
      rr: '',
      spo2: '',
      temp: '',
      airway: '',
      rs: '',
      cvs: '',
      pa: '',
      cns: '',
      pain: '',
      localExamination: '',
      investigationDone: ''
    });
  };

  return (
    <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        {/* Top Row: Logos */}
        <View style={styles.headerTopRow}>
          <Image
            source={require('../assets/Logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          {/* NABH logo - assuming it's in assets/nabh-logo.png. If not, please provide it. */}
          {/*
          <Image
            source={require('../assets/nabh-logo.png')}
            style={styles.nabhLogo}
            resizeMode="contain"
          />
          */}
        </View>

        {/* Centered Text */}
        <View style={styles.headerCenteredText}>
          <Text style={styles.headerTitle}>CURA HOSPITALS</Text>
          <Text style={styles.headerSubtitle}>INPATIENT INITIAL ASSESSMENT</Text>
          <Text style={styles.headerSubtitle}>FORM(2)</Text>
        </View>
      </View>

      {/* Family/Social/Occupational History & Allergies Section */}
      <View style={[styles.section, styles.twoColumnSection]}>
        <View style={styles.column}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Family/Social/Occupational History:</Text>
          </View>
          <View style={styles.sectionContent}>
            <TextInput
              style={[styles.input, styles.textArea]}
              multiline
              numberOfLines={10}
              placeholder="Enter history..."
                value={formState.familyHistory}
                onChangeText={(text) => setFormState(prev => ({ ...prev, familyHistory: text }))}
            />
          </View>
        </View>
        <View style={styles.column}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Allergies:</Text>
          </View>
          <View style={styles.sectionContent}>
            <TextInput
              style={[styles.input, styles.textArea]}
              multiline
              numberOfLines={10}
              placeholder="Enter allergies..."
                value={formState.allergies}
                onChangeText={(text) => setFormState(prev => ({ ...prev, allergies: text }))}
            />
          </View>
        </View>
      </View>

      {/* General Physical Examination Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>General Physical Examination:</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.checkboxContainer}>
              <TouchableOpacity onPress={() => setFormState(prev => ({ ...prev, pallorChecked: !prev.pallorChecked }))}>
                <Text style={styles.checkboxText}>{formState.pallorChecked ? '☑ Pallor' : '□ Pallor'}</Text>
            </TouchableOpacity>
              <TouchableOpacity onPress={() => setFormState(prev => ({ ...prev, icterusChecked: !prev.icterusChecked }))}>
                <Text style={styles.checkboxText}>{formState.icterusChecked ? '☑ Icterus' : '□ Icterus'}</Text>
            </TouchableOpacity>
              <TouchableOpacity onPress={() => setFormState(prev => ({ ...prev, cyanosisChecked: !prev.cyanosisChecked }))}>
                <Text style={styles.checkboxText}>{formState.cyanosisChecked ? '☑ Cyanosis' : '□ Cyanosis'}</Text>
            </TouchableOpacity>
              <TouchableOpacity onPress={() => setFormState(prev => ({ ...prev, clubbingChecked: !prev.clubbingChecked }))}>
                <Text style={styles.checkboxText}>{formState.clubbingChecked ? '☑ Clubbing' : '□ Clubbing'}</Text>
            </TouchableOpacity>
              <TouchableOpacity onPress={() => setFormState(prev => ({ ...prev, lymphadenopathyChecked: !prev.lymphadenopathyChecked }))}>
                <Text style={styles.checkboxText}>{formState.lymphadenopathyChecked ? '☑ Lymphadenopathy' : '□ Lymphadenopathy'}</Text>
            </TouchableOpacity>
              <TouchableOpacity onPress={() => setFormState(prev => ({ ...prev, pedalEdemaChecked: !prev.pedalEdemaChecked }))}>
                <Text style={styles.checkboxText}>{formState.pedalEdemaChecked ? '☑ Pedal Edema' : '□ Pedal Edema'}</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>□ Others:</Text>
            <TextInput
              style={styles.input}
              placeholder="Specify others..."
                value={formState.others}
                onChangeText={(text) => setFormState(prev => ({ ...prev, others: text }))}
            />
          </View>
        </View>
      </View>

      {/* Vitals Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Vitals:</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.vitalsRow}>
            <Text style={styles.label}>HR:</Text>
              <TextInput 
                style={styles.vitalsInput} 
                value={formState.hr}
                onChangeText={(text) => setFormState(prev => ({ ...prev, hr: text }))}
              />
            <Text style={styles.vitalsUnit}>/min</Text>
            <Text style={styles.label}>BP:</Text>
              <TextInput 
                style={styles.vitalsInput} 
                value={formState.bp}
                onChangeText={(text) => setFormState(prev => ({ ...prev, bp: text }))}
              />
            <Text style={styles.vitalsUnit}>mmHg</Text>
            <Text style={styles.label}>RR:</Text>
              <TextInput 
                style={styles.vitalsInput} 
                value={formState.rr}
                onChangeText={(text) => setFormState(prev => ({ ...prev, rr: text }))}
              />
            <Text style={styles.vitalsUnit}>/min</Text>
            <Text style={styles.label}>SpO2:</Text>
              <TextInput 
                style={styles.vitalsInput} 
                value={formState.spo2}
                onChangeText={(text) => setFormState(prev => ({ ...prev, spo2: text }))}
              />
            <Text style={styles.vitalsUnit}>%</Text>
            <Text style={styles.label}>Temp:</Text>
              <TextInput 
                style={styles.vitalsInput} 
                value={formState.temp}
                onChangeText={(text) => setFormState(prev => ({ ...prev, temp: text }))}
              />
            <Text style={styles.vitalsUnit}>°F</Text>
          </View>
        </View>
      </View>

      {/* Systemic Examination & Investigation Done Section */}
      <View style={[styles.section, styles.twoColumnSection]}>
        <View style={styles.column}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Systemic Examination:</Text>
          </View>
          <View style={styles.sectionContent}>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>Airway:</Text>
                <TextInput 
                  style={styles.input} 
                  value={formState.airway}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, airway: text }))}
                />
            </View>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>RS:</Text>
                <TextInput 
                  style={styles.input} 
                  value={formState.rs}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, rs: text }))}
                />
            </View>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>CVS:</Text>
                <TextInput 
                  style={styles.input} 
                  value={formState.cvs}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, cvs: text }))}
                />
            </View>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>P/A:</Text>
                <TextInput 
                  style={styles.input} 
                  value={formState.pa}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, pa: text }))}
                />
            </View>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>CNS:</Text>
                <TextInput 
                  style={styles.input} 
                  value={formState.cns}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, cns: text }))}
                />
            </View>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>Pain:</Text>
                <TextInput 
                  style={styles.input} 
                  value={formState.pain}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, pain: text }))}
                />
            </View>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>Local Examination:</Text>
                <TextInput 
                  style={styles.input} 
                  value={formState.localExamination}
                  onChangeText={(text) => setFormState(prev => ({ ...prev, localExamination: text }))}
                />
            </View>
          </View>
        </View>
        <View style={styles.column}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Investigation Done:</Text>
          </View>
          <View style={styles.sectionContent}>
            <TextInput
              style={[styles.input, styles.textArea]}
              multiline
              numberOfLines={20}
              placeholder="Enter investigation details..."
                value={formState.investigationDone}
                onChangeText={(text) => setFormState(prev => ({ ...prev, investigationDone: text }))}
            />
          </View>
        </View>
      </View>
    </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 10,
  },
  header: {
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#000',
    padding: 5,
  },
  headerTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  logoContainer: { // Keeping for reference of original header
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 350, // Increased width for consistency with other pages
    height: 100,
  },
  nabhLogo: {
    width: 80,
    height: 80,
  },
  headerCenteredText: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 16,
  },
  section: {
    marginBottom: 10, // Adjusted margin for consistency
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 0, // Removed border radius for consistency
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionContent: {
    padding: 8,
  },
  fieldContainer: {
    marginBottom: 10,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 5,
    fontSize: 14,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  twoColumnSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10, // Adjusted margin for consistency
    borderWidth: 0, // Removed border, handled by inner columns
  },
  column: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#000',
    marginHorizontal: 0, // Removed horizontal margin for consistency
    marginRight: 10, // Added right margin for separation
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  checkboxText: {
    fontSize: 14,
    marginRight: 15,
    marginBottom: 5,
  },
  vitalsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  vitalsInput: {
    borderBottomWidth: 1,
    borderColor: '#000',
    padding: 2,
    minWidth: 50,
    marginRight: 5,
    textAlign: 'center',
    fontSize: 14,
  },
  vitalsUnit: {
    fontSize: 12,
    marginRight: 10,
  },
});

export default DiagnosisPage2;