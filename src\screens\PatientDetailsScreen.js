import React, { useEffect, useState, useRef } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Animated,
  Dimensions,
  Platform,
  FlatList
} from 'react-native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { supabase } from '../utils/supabase';
import * as Linking from 'expo-linking';
import { COLORS } from '../theme';
import { Easing } from 'react-native';

const { width } = Dimensions.get('window');

const PatientDetailsScreen = ({ route, navigation }) => {
  const { patient: initialPatient, appointments: initialAppointments } = route.params || { 
    patient: { 
      name: 'Patient Name', 
      mrno: '12345',
      age: 30,
      gender: 'Male',
      contact: '************',
      medical_history: 'None'
    },
    appointments: []
  };

  const [appointments, setAppointments] = useState(initialAppointments);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAppointments = async () => {
      setLoading(true);
      const { data, error } = await supabase
        .from('appointments')
        .select('appointment_id, date, time, patient_name, symptoms_brief, prescription_file_path')
        .eq('mrno', initialPatient.mrno)
        .order('date', { ascending: false })
        .order('time', { ascending: false });
      if (!error && data) {
        setAppointments(data);
      }
      setLoading(false);
    };
    fetchAppointments();
  }, [initialPatient.mrno]);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleDiagnosis = async () => {
    // Fetch latest appointment for this patient
    const { data: latest, error } = await supabase
      .from('appointments')
      .select('appointment_id, prescription_file_path')
      .eq('mrno', initialPatient.mrno)
      .order('date', { ascending: false })
      .order('time', { ascending: false })
      .limit(1)
      .single();
    // If there is no prescription_file_path, it's a new appointment (empty form)
    const isNewAppointment = !latest || !latest.prescription_file_path;
    navigation.navigate('DiagnosisFormScreen', { mrno: initialPatient.mrno, isNewAppointment });
  };

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    // Animate on mount
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const renderAppointmentItem = ({ item, index }) => (
    <Animated.View 
      style={[
        styles.visitItem,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }
      ]}
    >
      <View style={styles.visitHeader}>
        <View>
          <Text style={styles.visitDate}>{item.date}</Text>
          <Text style={styles.visitTime}>{item.time}</Text>
        </View>
        <View style={[
          styles.statusBadge,
          { backgroundColor: item.status === 'completed' ? COLORS.success : COLORS.warning }
        ]}>
          <Text style={styles.statusText}>{item.status || 'pending'}</Text>
        </View>
      </View>
      
      <View style={styles.visitContent}>
        <Text style={styles.visitReason}>
          {item.symptoms_brief || 'No symptoms described'}
        </Text>
        <View style={styles.actionButtons}>
          {item.prescription_file_path ? (
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={() => Linking.openURL(item.prescription_file_path)}
            >
              <Ionicons name="document-text-outline" size={16} color="#fff" />
              <Text style={styles.actionButtonText}>Diagnostics</Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.noDiagnosticsContainer}>
              <Ionicons name="document-text-outline" size={16} color={COLORS.gray} />
              <Text style={styles.noDiagnosticsText}>No Diagnostics</Text>
            </View>
          )}
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={() => navigation.navigate('PrescriptionScreen', { 
              appointment_id: item.appointment_id, 
              doctor_id: (route.params?.doctor?.doctor_id || null) 
            })}
          >
            <Ionicons name="medical-outline" size={16} color="#fff" />
            <Text style={styles.actionButtonText}>Prescription</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Animated.View 
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }
        ]}
      >
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={handleBack}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Patient Details</Text>
        <TouchableOpacity 
          style={styles.searchButton}
          onPress={() => navigation.navigate('PatientSearch')}
          activeOpacity={0.7}
        >
          <Ionicons name="search" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </Animated.View>

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View 
          style={[
            styles.patientInfoCard,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }
          ]}
        >
          <View style={styles.patientHeader}>
            <View style={styles.avatar}>
              <Ionicons name="person" size={40} color={COLORS.primary} />
            </View>
            <View style={styles.patientInfo}>
              <Text style={styles.patientName}>{initialPatient.name}</Text>
              <Text style={styles.patientId}>MR No: {initialPatient.mrno}</Text>
            </View>
          </View>
          
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Age</Text>
              <Text style={styles.infoValue}>{initialPatient.age || 'N/A'}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Gender</Text>
              <Text style={styles.infoValue}>{initialPatient.gender || 'N/A'}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Contact</Text>
              <Text style={styles.infoValue}>{initialPatient.contact || 'N/A'}</Text>
            </View>
          </View>
          
          {initialPatient.medical_history && (
            <View style={styles.medicalHistoryContainer}>
              <Text style={styles.sectionTitle}>Medical History</Text>
              <Text style={styles.medicalHistoryText}>{initialPatient.medical_history}</Text>
            </View>
          )}
        </Animated.View>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity
            style={[styles.mainActionButton, { backgroundColor: COLORS.secondary }]}
            onPress={() => navigation.navigate('LabRecordScreen', { patient: initialPatient })}
            activeOpacity={0.8}
          >
            <Ionicons name="flask-outline" size={20} color="#fff" />
            <Text style={styles.mainActionButtonText}>Lab Records</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.mainActionButton, { backgroundColor: COLORS.primary }]}
            onPress={handleDiagnosis}
            activeOpacity={0.8}
          >
            <Ionicons name="medical-outline" size={20} color="#fff" />
            <Text style={styles.mainActionButtonText}>New Diagnosis</Text>
          </TouchableOpacity>
        </View>

        <Animated.View 
          style={[
            styles.sectionCard,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }
          ]}
        >
          <View style={styles.sectionHeader}>
            <Ionicons name="calendar" size={20} color={COLORS.primary} />
            <Text style={styles.sectionTitle}>Appointment History</Text>
          </View>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={COLORS.primary} />
              <Text style={styles.loadingText}>Loading appointments...</Text>
            </View>
          ) : appointments && appointments.length > 0 ? (
            <FlatList
              data={appointments}
              renderItem={renderAppointmentItem}
              keyExtractor={(item, index) => `appt-${index}`}
              scrollEnabled={false}
              contentContainerStyle={styles.appointmentList}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="calendar-outline" size={48} color={COLORS.grayLight} />
              <Text style={styles.emptyText}>No appointments found</Text>
              <Text style={styles.emptySubtext}>This patient doesn't have any appointments yet.</Text>
            </View>
          )}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'android' ? 16 : 0,
    paddingBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: COLORS.white,
    textAlign: 'center',
    flex: 1,
    marginRight: 40, // To balance the back button space
  },
  backButton: {
    padding: 8,
    zIndex: 1,
  },
  searchButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
    paddingTop: 16,
  },
  patientInfoCard: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  patientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(138, 99, 207, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: 22,
    fontFamily: 'Inter-SemiBold',
    color: COLORS.textPrimary,
    marginBottom: 4,
  },
  patientId: {
    fontSize: 14,
    color: COLORS.gray,
    fontFamily: 'Inter-Regular',
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
    marginBottom: 12,
  },
  infoItem: {
    width: '33.33%',
    paddingHorizontal: 8,
    marginBottom: 16,
  },
  infoLabel: {
    fontSize: 12,
    color: COLORS.gray,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  infoValue: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: COLORS.textPrimary,
  },
  medicalHistoryContainer: {
    marginTop: 8,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  medicalHistoryText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  sectionCard: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: COLORS.textPrimary,
    marginLeft: 8,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: COLORS.gray,
    fontFamily: 'Inter-Medium',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: COLORS.textPrimary,
    marginTop: 12,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.gray,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
    textAlign: 'center',
    maxWidth: '80%',
  },
  appointmentList: {
    paddingBottom: 8,
  },
  visitItem: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  visitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  visitDate: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: COLORS.textPrimary,
  },
  visitTime: {
    fontSize: 13,
    color: COLORS.gray,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: COLORS.white,
    textTransform: 'capitalize',
  },
  visitContent: {
    marginTop: 4,
  },
  visitReason: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    backgroundColor: COLORS.secondary,
  },
  actionButtonText: {
    color: COLORS.white,
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
    marginLeft: 6,
  },
  noDiagnosticsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: COLORS.background,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  noDiagnosticsText: {
    fontSize: 14,
    color: COLORS.gray,
    fontFamily: 'Inter-Medium',
    marginLeft: 6,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    marginTop: 8,
    backgroundColor: COLORS.background,
    padding: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mainActionButton: {
    width: '48%',
    minHeight: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  mainActionButtonText: {
    color: COLORS.white,
    fontFamily: 'Inter-SemiBold',
    fontSize: 15,
    marginLeft: 8,
  },
});

export default PatientDetailsScreen;
