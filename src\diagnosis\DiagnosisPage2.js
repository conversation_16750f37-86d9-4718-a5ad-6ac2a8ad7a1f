import React from 'react';
import { Image, StyleSheet, Text, TextInput, View } from 'react-native';

const DiagnosisPage2 = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/Logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>DIAGNOSIS FORM (2)</Text>
        </View>
      </View>

      {/* Laboratory Results Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Laboratory Results</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Blood Tests:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter blood test results"
              multiline
              numberOfLines={4}
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Urine Analysis:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter urine analysis results"
              multiline
              numberOfLines={4}
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Other Tests:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter other test results"
              multiline
              numberOfLines={4}
            />
          </View>
        </View>
      </View>

      {/* Imaging Results Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Imaging Results</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>X-Ray:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter X-ray results"
              multiline
              numberOfLines={4}
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>CT Scan:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter CT scan results"
              multiline
              numberOfLines={4}
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>MRI:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter MRI results"
              multiline
              numberOfLines={4}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
    paddingBottom: 10,
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 150,
    height: 60,
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    paddingLeft: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  section: {
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#000',
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  sectionContent: {
    padding: 5,
  },
  fieldContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
});

export default DiagnosisPage2; 