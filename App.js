import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import screens
import AppointmentsScreen from './src/screens/AppointmentsScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import DiagnosisAnnotateScreen from './src/screens/DiagnosisAnnotateScreen';
import DiagnosisFormScreen from './src/screens/DiagnosisFormScreen';
import DiagnosisMultiAnnotateScreen from './src/screens/DiagnosisMultiAnnotateScreen';
import LoginScreen from './src/screens/LoginScreen';
import PatientDetailsScreen from './src/screens/PatientDetailsScreen';
import PatientSearchScreen from './src/screens/PatientSearchScreen';
import QRScannerScreen from './src/screens/QRScannerScreen';
import RecentVisitsScreen from './src/screens/RecentVisitsScreen';
import PrescriptionScreen from './src/screens/PrescriptionScreen';
import LabRecordScreen from './src/screens/LabRecordScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <Stack.Navigator 
          initialRouteName="Login"
          screenOptions={{
            headerShown: false,
            cardStyle: { backgroundColor: '#fff' }
          }}
        >
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Dashboard" component={DashboardScreen} />
          <Stack.Screen name="QRScanner" component={QRScannerScreen} />
          <Stack.Screen name="PatientDetails" component={PatientDetailsScreen} />
          <Stack.Screen name="Appointments" component={AppointmentsScreen} />
          <Stack.Screen name="PatientSearch" component={PatientSearchScreen} />
          <Stack.Screen name="DiagnosisMultiAnnotateScreen" component={DiagnosisMultiAnnotateScreen} />
          <Stack.Screen name="DiagnosisAnnotateScreen" component={DiagnosisAnnotateScreen} />
          <Stack.Screen name="DiagnosisFormScreen" component={DiagnosisFormScreen} />
          <Stack.Screen name="RecentVisits" component={RecentVisitsScreen} />
          <Stack.Screen name="PrescriptionScreen" component={PrescriptionScreen} />
          <Stack.Screen name="LabRecordScreen" component={LabRecordScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
