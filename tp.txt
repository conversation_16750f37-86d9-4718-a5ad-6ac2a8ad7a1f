OK so now this is the second part of a saas i'm building. 
The OPD-APP:
Features -> a qr code scanner, a pdf with handwriting feature for diagnosis, a prescription page 

workflow:
1) First the doctor will login in(already build)
2) Next a dashboard for doctor will appear with docotor details which will have options like (patients records, 
    appointments (completed, pending), a scan qr code, a logout button )
3) now in the backend if a new record is added for the doctor in the appointments table it should show a notification
 for the doctor.
4) now after the patient scan the qr code a page should appear with three options (prescription, lab test and diagnosis)
5) On clicking on the diagnosis it should open a pdf fetching from the database (pdf stored in onedrive) and it should have 
    a open to enable pen writing on it which the doctor will write and on clicking on save it will update in the database

Build till here further will be described later 