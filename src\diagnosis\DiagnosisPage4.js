import React from 'react';
import { Image, StyleSheet, Text, TextInput, View } from 'react-native';

const DiagnosisPage4 = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/Logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>DIAGNOSIS FORM (4)</Text>
        </View>
      </View>

      {/* Emergency Contact Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Emergency Contact Information</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Contact Name:</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter emergency contact name"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Relationship:</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter relationship to patient"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Phone Number:</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter emergency contact phone number"
              keyboardType="phone-pad"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Address:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter emergency contact address"
              multiline
              numberOfLines={3}
            />
          </View>
        </View>
      </View>

      {/* Hospital Contact Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Hospital Contact Information</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Hospital Name:</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter hospital name"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Department:</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter department name"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Phone Number:</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter hospital phone number"
              keyboardType="phone-pad"
            />
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Address:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter hospital address"
              multiline
              numberOfLines={3}
            />
          </View>
        </View>
      </View>

      {/* Additional Notes Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Additional Notes</Text>
        </View>
        <View style={styles.sectionContent}>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Notes:</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter any additional notes or instructions"
              multiline
              numberOfLines={6}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
    paddingBottom: 10,
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 150,
    height: 60,
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    paddingLeft: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  section: {
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#000',
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  sectionContent: {
    padding: 5,
  },
  fieldContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
});

export default DiagnosisPage4; 