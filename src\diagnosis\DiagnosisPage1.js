import React from 'react';
import { Image, StyleSheet, Text, TextInput, View } from 'react-native';

const DiagnosisPage1 = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/Logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>INPATIENT INITIAL ASSESSMENT FORM(1)</Text>
        </View>
      </View>

      {/* Presenting Complaints Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Presenting Complaints</Text>
        </View>
        <View style={styles.sectionContent}>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={8}
            placeholder="Enter presenting complaints..."
          />
        </View>
      </View>

      {/* History of Presenting Complaints Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>History of Presenting Complaints</Text>
        </View>
        <View style={styles.sectionContent}>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={8}
            placeholder="Enter history of presenting complaints..."
          />
        </View>
      </View>

      {/* Past Medical/Surgical History Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Past Medical/Surgical History</Text>
        </View>
        <View style={styles.sectionContent}>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={8}
            placeholder="Enter past medical/surgical history..."
          />
        </View>
      </View>

      {/* Regular Medication Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Regular Medication</Text>
        </View>
        <View style={styles.sectionContent}>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={8}
            placeholder="Enter regular medications..."
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
    paddingBottom: 10,
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logo: {
    width: 150,
    height: 60,
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    paddingLeft: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  section: {
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 4,
  },
  sectionHeader: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionContent: {
    padding: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  textArea: {
    minHeight: 120,
    textAlignVertical: 'top',
  },
});

export default DiagnosisPage1; 